import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconAntennaBarsOff.mjs
var IconAntennaBarsOff = createReactComponent("outline", "antenna-bars-off", "IconAntennaBarsOff", [["path", { "d": "M6 18v-3", "key": "svg-0" }], ["path", { "d": "M10 18v-6", "key": "svg-1" }], ["path", { "d": "M14 18v-4", "key": "svg-2" }], ["path", { "d": "M14 10v-1", "key": "svg-3" }], ["path", { "d": "M18 14v-8", "key": "svg-4" }], ["path", { "d": "M3 3l18 18", "key": "svg-5" }]]);

export {
  IconAntennaBarsOff
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconAntennaBarsOff.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZZB4NMM2.js.map
