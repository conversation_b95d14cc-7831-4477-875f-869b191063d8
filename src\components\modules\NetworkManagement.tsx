import React, { useState, useEffect } from 'react';
import {
  Stack,
  Tabs,
  Paper,
  Title,
  Text,
  Group,
  Badge,
  Alert,
  Button,
  ActionIcon,
} from '@mantine/core';
import {
  IconNetwork,
  IconSettings,
  IconActivity,
  IconTopologyBus as IconTopology,
  IconRefresh,
  IconAlertTriangle,
  IconCheck,
  IconX,
} from '@tabler/icons-react';
import { NetworkConfiguration } from '../network/NetworkConfiguration';
import { NetworkTopology } from '../network/NetworkTopology';
import { NetworkDashboard } from '../network/NetworkDashboard';
import { useNetworkStore } from '../../stores/networkStore';
import { useAuthStore } from '../../stores/authStore';
import { notifications } from '@mantine/notifications';

export function NetworkManagement() {
  const [activeTab, setActiveTab] = useState<string>('dashboard');
  const { user, company, branch } = useAuthStore();
  const {
    networkHealth,
    configuration,
    isInitializing,
    error,
    initialize,
    refreshNetworkData,
    checkHealth,
  } = useNetworkStore();

  useEffect(() => {
    initializeNetwork();
  }, []);

  useEffect(() => {
    // Periodic health check every 30 seconds
    const healthCheckInterval = setInterval(async () => {
      await checkHealth();
    }, 30000);

    return () => clearInterval(healthCheckInterval);
  }, [checkHealth]);

  const initializeNetwork = async () => {
    if (user && company && branch) {
      const branchId = user.type === 'EMPLOYEE' ? user.employee?.branchId || branch.id : branch.id;
      const companyId = user.type === 'OWNER' ? user.owner?.companyId || company.id : 
                       user.type === 'EMPLOYEE' ? user.employee?.companyId || company.id : company.id;
      
      const success = await initialize(branchId, companyId);
      
      if (!success) {
        notifications.show({
          title: 'فشل في تهيئة الشبكة',
          message: 'تعذر تهيئة خدمات الشبكة. يرجى التحقق من إعدادات ZeroTier.',
          color: 'red',
          icon: <IconX size="1rem" />,
        });
      }
    }
  };

  const handleRefresh = async () => {
    await refreshNetworkData();
    notifications.show({
      title: 'تم تحديث البيانات',
      message: 'تم تحديث بيانات الشبكة بنجاح',
      color: 'blue',
      icon: <IconCheck size="1rem" />,
    });
  };

  const getHealthStatusColor = (health: typeof networkHealth) => {
    if (!health) return 'gray';
    
    const issues = [
      !health.isZeroTierRunning,
      !health.isConnectedToNetwork,
      !health.discoveryServiceRunning,
      health.averageLatency > 1000,
    ].filter(Boolean).length;

    if (issues === 0) return 'green';
    if (issues <= 2) return 'yellow';
    return 'red';
  };

  const getHealthStatusLabel = (health: typeof networkHealth) => {
    if (!health) return 'غير معروف';
    
    const issues = [
      !health.isZeroTierRunning,
      !health.isConnectedToNetwork,
      !health.discoveryServiceRunning,
      health.averageLatency > 1000,
    ].filter(Boolean).length;

    if (issues === 0) return 'ممتاز';
    if (issues <= 2) return 'جيد';
    return 'يحتاج انتباه';
  };

  if (isInitializing) {
    return (
      <Paper withBorder p="xl" ta="center">
        <Stack align="center" gap="md">
          <IconNetwork size="3rem" color="blue" />
          <Title order={3}>جاري تهيئة خدمات الشبكة...</Title>
          <Text c="dimmed">يرجى الانتظار بينما نقوم بتهيئة اتصالات الشبكة</Text>
        </Stack>
      </Paper>
    );
  }

  return (
    <Stack gap="lg">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={2}>إدارة الشبكة</Title>
          <Text c="dimmed">إدارة اتصالات ZeroTier والتواصل بين الفروع</Text>
        </div>
        
        <Group gap="md">
          {networkHealth && (
            <Badge 
              color={getHealthStatusColor(networkHealth)} 
              variant="light" 
              size="lg"
              leftSection={
                getHealthStatusColor(networkHealth) === 'green' ? 
                <IconCheck size="1rem" /> : 
                <IconAlertTriangle size="1rem" />
              }
            >
              حالة الشبكة: {getHealthStatusLabel(networkHealth)}
            </Badge>
          )}
          
          <ActionIcon variant="light" size="lg" onClick={handleRefresh}>
            <IconRefresh size="1.2rem" />
          </ActionIcon>
        </Group>
      </Group>

      {/* Error Alert */}
      {error && (
        <Alert color="red" icon={<IconAlertTriangle size="1rem" />} onClose={() => {}}>
          <Text fw={500}>خطأ في الشبكة</Text>
          <Text size="sm">{error}</Text>
        </Alert>
      )}

      {/* Network Health Summary */}
      {networkHealth && (
        <Paper withBorder p="md">
          <Group justify="space-between">
            <Group gap="xl">
              <div>
                <Text size="sm" c="dimmed">ZeroTier</Text>
                <Badge color={networkHealth.isZeroTierRunning ? 'green' : 'red'} variant="light">
                  {networkHealth.isZeroTierRunning ? 'يعمل' : 'متوقف'}
                </Badge>
              </div>
              
              <div>
                <Text size="sm" c="dimmed">الاتصال بالشبكة</Text>
                <Badge color={networkHealth.isConnectedToNetwork ? 'green' : 'red'} variant="light">
                  {networkHealth.isConnectedToNetwork ? 'متصل' : 'غير متصل'}
                </Badge>
              </div>
              
              <div>
                <Text size="sm" c="dimmed">اكتشاف الفروع</Text>
                <Badge color={networkHealth.discoveryServiceRunning ? 'green' : 'red'} variant="light">
                  {networkHealth.discoveryServiceRunning ? 'نشط' : 'متوقف'}
                </Badge>
              </div>
              
              <div>
                <Text size="sm" c="dimmed">الفروع المتصلة</Text>
                <Text fw={500}>{networkHealth.connectedBranches}</Text>
              </div>
              
              <div>
                <Text size="sm" c="dimmed">متوسط الاستجابة</Text>
                <Text fw={500} c={networkHealth.averageLatency < 100 ? 'green' : 
                                  networkHealth.averageLatency < 500 ? 'orange' : 'red'}>
                  {networkHealth.averageLatency}ms
                </Text>
              </div>
            </Group>
            
            <Text size="xs" c="dimmed">
              آخر فحص: {networkHealth.lastHealthCheck.toLocaleTimeString('ar-SA')}
            </Text>
          </Group>
        </Paper>
      )}

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onChange={(value) => setActiveTab(value || 'dashboard')}>
        <Tabs.List>
          <Tabs.Tab value="dashboard" leftSection={<IconActivity size="1rem" />}>
            لوحة الإحصائيات
          </Tabs.Tab>
          <Tabs.Tab value="topology" leftSection={<IconTopology size="1rem" />}>
            مخطط الشبكة
          </Tabs.Tab>
          <Tabs.Tab value="configuration" leftSection={<IconSettings size="1rem" />}>
            الإعدادات
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="dashboard" pt="lg">
          <NetworkDashboard />
        </Tabs.Panel>

        <Tabs.Panel value="topology" pt="lg">
          <NetworkTopology 
            currentBranchId={
              user?.type === 'EMPLOYEE' ? user.employee?.branchId || branch?.id || '' : 
              branch?.id || ''
            } 
          />
        </Tabs.Panel>

        <Tabs.Panel value="configuration" pt="lg">
          <NetworkConfiguration
            branchId={
              user?.type === 'EMPLOYEE' ? user.employee?.branchId || branch?.id || '' : 
              branch?.id || ''
            }
            companyId={
              user?.type === 'OWNER' ? user.owner?.companyId || company?.id || '' :
              user?.type === 'EMPLOYEE' ? user.employee?.companyId || company?.id || '' :
              company?.id || ''
            }
            onNetworkConfigured={(networkId) => {
              notifications.show({
                title: 'تم تكوين الشبكة',
                message: `تم الانضمام بنجاح للشبكة ${networkId}`,
                color: 'green',
                icon: <IconCheck size="1rem" />,
              });
              refreshNetworkData();
            }}
          />
        </Tabs.Panel>
      </Tabs>

      {/* Quick Actions */}
      {!configuration && (
        <Alert color="blue" icon={<IconNetwork size="1rem" />}>
          <Group justify="space-between">
            <div>
              <Text fw={500}>لم يتم تكوين الشبكة بعد</Text>
              <Text size="sm">يرجى الانتقال إلى تبويب الإعدادات لتكوين اتصال ZeroTier</Text>
            </div>
            <Button variant="light" onClick={() => setActiveTab('configuration')}>
              تكوين الآن
            </Button>
          </Group>
        </Alert>
      )}
    </Stack>
  );
}
