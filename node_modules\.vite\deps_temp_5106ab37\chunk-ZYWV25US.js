import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconSquareRoundedLetterUFilled.mjs
var IconSquareRoundedLetterUFilled = createReactComponent("filled", "square-rounded-letter-u-filled", "IconSquareRoundedLetterUFilled", [["path", { "d": "M11.676 2.001l.324 -.001c7.752 0 10 2.248 10 10l-.005 .642c-.126 7.235 -2.461 9.358 -9.995 9.358l-.642 -.005c-7.13 -.125 -9.295 -2.395 -9.358 -9.67v-.325c0 -7.643 2.185 -9.936 9.676 -9.999m2.324 4.999a1 1 0 0 0 -1 1v6a1 1 0 0 1 -2 0v-6a1 1 0 0 0 -2 0v6a3 3 0 0 0 6 0v-6a1 1 0 0 0 -1 -1", "key": "svg-0" }]]);

export {
  IconSquareRoundedLetterUFilled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconSquareRoundedLetterUFilled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZYWV25US.js.map
