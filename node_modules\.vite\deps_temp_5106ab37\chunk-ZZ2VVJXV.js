import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconInbox.mjs
var IconInbox = createReactComponent("outline", "inbox", "IconInbox", [["path", { "d": "M4 4m0 2a2 2 0 0 1 2 -2h12a2 2 0 0 1 2 2v12a2 2 0 0 1 -2 2h-12a2 2 0 0 1 -2 -2z", "key": "svg-0" }], ["path", { "d": "M4 13h3l3 3h4l3 -3h3", "key": "svg-1" }]]);

export {
  IconInbox
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconInbox.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZZ2VVJXV.js.map
