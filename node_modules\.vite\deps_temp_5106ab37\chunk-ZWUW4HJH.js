import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconDisabled.mjs
var IconDisabled = createReactComponent("outline", "disabled", "IconDisabled", [["path", { "d": "M11 5m-2 0a2 2 0 1 0 4 0a2 2 0 1 0 -4 0", "key": "svg-0" }], ["path", { "d": "M11 7l0 8l4 0l4 5", "key": "svg-1" }], ["path", { "d": "M11 11l5 0", "key": "svg-2" }], ["path", { "d": "M7 11.5a5 5 0 1 0 6 7.5", "key": "svg-3" }]]);

export {
  IconDisabled
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconDisabled.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZWUW4HJH.js.map
