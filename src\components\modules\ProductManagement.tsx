import React, { useState, useEffect } from 'react';
import {
  Stack,
  Tabs,
  Paper,
  Title,
  Text,
  Group,
  Button,
  Modal,
  Grid,
  Card,
  Badge,
  ActionIcon,
  Tooltip,
  Alert,
  Divider,
} from '@mantine/core';
import {
  IconPackage,
  IconSearch,
  IconPlus,
  IconCategory,
  IconTemplate,
  IconChartBar,
  IconSettings,
  IconEye,
  IconEdit,
  IconTrash,
  IconBarcode,
  IconPhoto,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';
import { ProductSearch } from '../products/ProductSearch';
import { ProductCategoriesTree, ProductCategory } from '../products/ProductCategoriesTree';
import { ProductForm, ProductFormData, ProductTemplate } from '../products/ProductForm';
import { ProductImageGallery } from '../products/ProductImageGallery';
import { productSearchService, ProductSearchResult } from '../../services/productSearchService';
import { unitConversionService } from '../../services/unitConversionService';

export function ProductManagement() {
  const [activeTab, setActiveTab] = useState<string>('search');
  const [showProductForm, setShowProductForm] = useState(false);
  const [showProductDetails, setShowProductDetails] = useState(false);
  const [editingProduct, setEditingProduct] = useState<ProductFormData | null>(null);
  const [selectedProduct, setSelectedProduct] = useState<ProductSearchResult | null>(null);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [templates, setTemplates] = useState<ProductTemplate[]>([]);
  const [suppliers, setSuppliers] = useState<Array<{ id: string; name: string; nameAr: string }>>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    try {
      // Load categories, templates, and suppliers
      // TODO: Replace with actual API calls
      setCategories(getMockCategories());
      setTemplates(getMockTemplates());
      setSuppliers(getMockSuppliers());
    } catch (error) {
      console.error('Failed to load initial data:', error);
    }
  };

  const getMockCategories = (): ProductCategory[] => [
    {
      id: 'engine',
      name: 'Engine Parts',
      nameAr: 'قطع المحرك',
      description: 'Engine components and parts',
      descriptionAr: 'مكونات وقطع المحرك',
      color: '#e74c3c',
      sortOrder: 1,
      isActive: true,
      productCount: 150,
      children: [
        {
          id: 'filters',
          parentId: 'engine',
          name: 'Filters',
          nameAr: 'الفلاتر',
          color: '#3498db',
          sortOrder: 1,
          isActive: true,
          productCount: 45,
        },
        {
          id: 'oils',
          parentId: 'engine',
          name: 'Oils',
          nameAr: 'الزيوت',
          color: '#f39c12',
          sortOrder: 2,
          isActive: true,
          productCount: 30,
        },
      ],
    },
    {
      id: 'brakes',
      name: 'Brake System',
      nameAr: 'نظام الفرامل',
      description: 'Brake components and parts',
      descriptionAr: 'مكونات وقطع نظام الفرامل',
      color: '#9b59b6',
      sortOrder: 2,
      isActive: true,
      productCount: 85,
    },
    {
      id: 'electrical',
      name: 'Electrical',
      nameAr: 'الكهرباء',
      description: 'Electrical components',
      descriptionAr: 'المكونات الكهربائية',
      color: '#2ecc71',
      sortOrder: 3,
      isActive: true,
      productCount: 120,
    },
  ];

  const getMockTemplates = (): ProductTemplate[] => [
    {
      id: 'oil-template',
      name: 'Oil Template',
      nameAr: 'قالب الزيوت',
      type: 'oil',
      fields: [
        {
          key: 'viscosity',
          label: 'Viscosity',
          labelAr: 'اللزوجة',
          type: 'select',
          required: true,
          options: [
            { value: '5W30', label: '5W-30', labelAr: '5W-30' },
            { value: '10W40', label: '10W-40', labelAr: '10W-40' },
            { value: '15W40', label: '15W-40', labelAr: '15W-40' },
            { value: '20W50', label: '20W-50', labelAr: '20W-50' },
          ],
        },
        {
          key: 'grade',
          label: 'Grade',
          labelAr: 'الدرجة',
          type: 'select',
          required: true,
          options: [
            { value: 'synthetic', label: 'Synthetic', labelAr: 'صناعي' },
            { value: 'semi-synthetic', label: 'Semi-Synthetic', labelAr: 'نصف صناعي' },
            { value: 'mineral', label: 'Mineral', labelAr: 'معدني' },
          ],
        },
        {
          key: 'capacity',
          label: 'Capacity (L)',
          labelAr: 'السعة (لتر)',
          type: 'number',
          required: true,
        },
      ],
    },
    {
      id: 'tire-template',
      name: 'Tire Template',
      nameAr: 'قالب الإطارات',
      type: 'tire',
      fields: [
        {
          key: 'size',
          label: 'Size',
          labelAr: 'المقاس',
          type: 'text',
          required: true,
        },
        {
          key: 'pattern',
          label: 'Pattern',
          labelAr: 'النقشة',
          type: 'text',
          required: false,
        },
        {
          key: 'season',
          label: 'Season',
          labelAr: 'الموسم',
          type: 'select',
          options: [
            { value: 'summer', label: 'Summer', labelAr: 'صيفي' },
            { value: 'winter', label: 'Winter', labelAr: 'شتوي' },
            { value: 'all-season', label: 'All Season', labelAr: 'جميع المواسم' },
          ],
        },
      ],
    },
    {
      id: 'battery-template',
      name: 'Battery Template',
      nameAr: 'قالب البطاريات',
      type: 'battery',
      fields: [
        {
          key: 'amperage',
          label: 'Amperage (Ah)',
          labelAr: 'الأمبير (أمبير/ساعة)',
          type: 'number',
          required: true,
        },
        {
          key: 'voltage',
          label: 'Voltage (V)',
          labelAr: 'الفولتية (فولت)',
          type: 'number',
          required: true,
        },
        {
          key: 'warranty',
          label: 'Warranty (months)',
          labelAr: 'الضمان (شهر)',
          type: 'number',
          required: false,
        },
        {
          key: 'type',
          label: 'Type',
          labelAr: 'النوع',
          type: 'select',
          options: [
            { value: 'maintenance-free', label: 'Maintenance Free', labelAr: 'خالية من الصيانة' },
            { value: 'wet', label: 'Wet Battery', labelAr: 'بطارية مبللة' },
            { value: 'gel', label: 'Gel Battery', labelAr: 'بطارية جل' },
          ],
        },
      ],
    },
  ];

  const getMockSuppliers = () => [
    { id: 'supplier-1', name: 'Auto Parts Co.', nameAr: 'شركة قطع السيارات' },
    { id: 'supplier-2', name: 'Engine Specialists', nameAr: 'متخصصو المحركات' },
    { id: 'supplier-3', name: 'Brake Masters', nameAr: 'أساتذة الفرامل' },
  ];

  const handleProductSelect = (product: ProductSearchResult) => {
    setSelectedProduct(product);
    setShowProductDetails(true);
  };

  const handleProductEdit = (product: ProductSearchResult) => {
    // Convert search result to form data
    const formData: ProductFormData = {
      id: product.id,
      name: product.name,
      nameAr: product.nameAr,
      sku: product.sku,
      barcode: product.barcode,
      brand: product.brand,
      brandAr: product.brandAr,
      model: product.model,
      baseUnitId: product.baseUnit,
      costPrice: product.costPrice,
      sellingPrice: product.sellingPrice,
      minStock: product.minStock,
      images: product.images,
      tags: [],
      oemNumbers: product.oemNumbers.map(oem => ({
        oemNumber: oem,
        manufacturer: '',
        manufacturerAr: '',
        isOriginal: false,
      })),
      suppliers: [],
      attributes: {},
      isActive: product.isActive,
      isFeatured: false,
    };

    setEditingProduct(formData);
    setShowProductForm(true);
  };

  const handleProductSave = async (data: ProductFormData) => {
    setLoading(true);
    try {
      // TODO: Implement actual save logic
      console.log('Saving product:', data);
      
      notifications.show({
        title: 'تم حفظ المنتج',
        message: 'تم حفظ بيانات المنتج بنجاح',
        color: 'green',
      });

      setShowProductForm(false);
      setEditingProduct(null);
    } catch (error) {
      console.error('Failed to save product:', error);
      notifications.show({
        title: 'فشل في حفظ المنتج',
        message: 'حدث خطأ أثناء حفظ المنتج',
        color: 'red',
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryCreate = async (category: Omit<ProductCategory, 'id'>) => {
    try {
      // TODO: Implement actual create logic
      const newCategory: ProductCategory = {
        ...category,
        id: `category-${Date.now()}`,
      };
      
      setCategories(prev => [...prev, newCategory]);
      
      notifications.show({
        title: 'تم إنشاء الفئة',
        message: 'تم إنشاء الفئة الجديدة بنجاح',
        color: 'green',
      });
    } catch (error) {
      console.error('Failed to create category:', error);
    }
  };

  const handleCategoryUpdate = async (id: string, updates: Partial<ProductCategory>) => {
    try {
      // TODO: Implement actual update logic
      setCategories(prev => 
        prev.map(cat => cat.id === id ? { ...cat, ...updates } : cat)
      );
      
      notifications.show({
        title: 'تم تحديث الفئة',
        message: 'تم تحديث الفئة بنجاح',
        color: 'green',
      });
    } catch (error) {
      console.error('Failed to update category:', error);
    }
  };

  const handleCategoryDelete = async (id: string) => {
    try {
      // TODO: Implement actual delete logic
      setCategories(prev => prev.filter(cat => cat.id !== id));
      
      notifications.show({
        title: 'تم حذف الفئة',
        message: 'تم حذف الفئة بنجاح',
        color: 'blue',
      });
    } catch (error) {
      console.error('Failed to delete category:', error);
    }
  };

  return (
    <Stack gap="lg">
      {/* Header */}
      <Group justify="space-between">
        <div>
          <Title order={2}>إدارة المنتجات</Title>
          <Text c="dimmed">إدارة شاملة للمنتجات والفئات ووحدات القياس</Text>
        </div>
        
        <Button
          leftSection={<IconPlus size="1rem" />}
          onClick={() => {
            setEditingProduct(null);
            setShowProductForm(true);
          }}
        >
          إضافة منتج جديد
        </Button>
      </Group>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onChange={setActiveTab}>
        <Tabs.List>
          <Tabs.Tab value="search" leftSection={<IconSearch size="1rem" />}>
            البحث والاستعراض
          </Tabs.Tab>
          <Tabs.Tab value="categories" leftSection={<IconCategory size="1rem" />}>
            الفئات
          </Tabs.Tab>
          <Tabs.Tab value="templates" leftSection={<IconTemplate size="1rem" />}>
            القوالب
          </Tabs.Tab>
          <Tabs.Tab value="analytics" leftSection={<IconChartBar size="1rem" />}>
            التحليلات
          </Tabs.Tab>
          <Tabs.Tab value="settings" leftSection={<IconSettings size="1rem" />}>
            الإعدادات
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value="search" pt="lg">
          <ProductSearch
            onProductSelect={handleProductSelect}
            onProductEdit={handleProductEdit}
            showActions={true}
          />
        </Tabs.Panel>

        <Tabs.Panel value="categories" pt="lg">
          <ProductCategoriesTree
            categories={categories}
            onCategoryCreate={handleCategoryCreate}
            onCategoryUpdate={handleCategoryUpdate}
            onCategoryDelete={handleCategoryDelete}
            showProductCounts={true}
            allowEdit={true}
          />
        </Tabs.Panel>

        <Tabs.Panel value="templates" pt="lg">
          <Paper withBorder p="lg">
            <Stack gap="md">
              <Group justify="space-between">
                <Title order={4}>قوالب المنتجات</Title>
                <Button leftSection={<IconPlus size="1rem" />}>
                  إضافة قالب جديد
                </Button>
              </Group>

              <Grid>
                {templates.map((template) => (
                  <Grid.Col key={template.id} span={4}>
                    <Card withBorder>
                      <Group justify="space-between" mb="md">
                        <div>
                          <Text fw={500}>{template.nameAr}</Text>
                          <Text size="sm" c="dimmed">{template.name}</Text>
                        </div>
                        <Badge color="blue" variant="light">
                          {template.type}
                        </Badge>
                      </Group>

                      <Text size="sm" c="dimmed" mb="md">
                        {template.fields.length} حقل مخصص
                      </Text>

                      <Group justify="flex-end">
                        <ActionIcon variant="light">
                          <IconEye size="1rem" />
                        </ActionIcon>
                        <ActionIcon variant="light" color="blue">
                          <IconEdit size="1rem" />
                        </ActionIcon>
                        <ActionIcon variant="light" color="red">
                          <IconTrash size="1rem" />
                        </ActionIcon>
                      </Group>
                    </Card>
                  </Grid.Col>
                ))}
              </Grid>
            </Stack>
          </Paper>
        </Tabs.Panel>

        <Tabs.Panel value="analytics" pt="lg">
          <Alert color="blue" title="قريباً">
            ستتوفر تحليلات المنتجات والمبيعات قريباً
          </Alert>
        </Tabs.Panel>

        <Tabs.Panel value="settings" pt="lg">
          <Alert color="blue" title="قريباً">
            ستتوفر إعدادات إدارة المنتجات قريباً
          </Alert>
        </Tabs.Panel>
      </Tabs>

      {/* Product Form Modal */}
      <Modal
        opened={showProductForm}
        onClose={() => {
          setShowProductForm(false);
          setEditingProduct(null);
        }}
        title={editingProduct ? 'تعديل المنتج' : 'إضافة منتج جديد'}
        size="xl"
        centered
      >
        <ProductForm
          product={editingProduct || undefined}
          categories={categories}
          templates={templates}
          suppliers={suppliers}
          onSubmit={handleProductSave}
          onCancel={() => {
            setShowProductForm(false);
            setEditingProduct(null);
          }}
          loading={loading}
        />
      </Modal>

      {/* Product Details Modal */}
      <Modal
        opened={showProductDetails}
        onClose={() => {
          setShowProductDetails(false);
          setSelectedProduct(null);
        }}
        title="تفاصيل المنتج"
        size="lg"
        centered
      >
        {selectedProduct && (
          <Stack gap="md">
            <Group justify="space-between">
              <div>
                <Title order={4}>{selectedProduct.nameAr}</Title>
                <Text c="dimmed">{selectedProduct.name}</Text>
              </div>
              <Group gap="xs">
                <ActionIcon
                  variant="light"
                  color="blue"
                  onClick={() => {
                    setShowProductDetails(false);
                    handleProductEdit(selectedProduct);
                  }}
                >
                  <IconEdit size="1rem" />
                </ActionIcon>
              </Group>
            </Group>

            <Divider />

            <Grid>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">رقم المنتج:</Text>
                <Text fw={500}>{selectedProduct.sku}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">الباركود:</Text>
                <Text fw={500}>{selectedProduct.barcode || 'غير محدد'}</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">السعر:</Text>
                <Text fw={500}>{selectedProduct.sellingPrice.toLocaleString('ar-SA')} ر.س</Text>
              </Grid.Col>
              <Grid.Col span={6}>
                <Text size="sm" c="dimmed">المخزون الحالي:</Text>
                <Text fw={500}>{selectedProduct.currentStock} {unitConversionService.formatUnit(selectedProduct.baseUnit)}</Text>
              </Grid.Col>
            </Grid>

            {selectedProduct.images.length > 0 && (
              <>
                <Divider />
                <ProductImageGallery
                  images={selectedProduct.images.map((url, index) => ({
                    id: `img-${index}`,
                    url,
                    filename: `صورة ${index + 1}`,
                    size: 0,
                  }))}
                  showUpload={false}
                  showDelete={false}
                />
              </>
            )}
          </Stack>
        )}
      </Modal>
    </Stack>
  );
}
