import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconArrowDownBar.mjs
var IconArrowDownBar = createReactComponent("outline", "arrow-down-bar", "IconArrowDownBar", [["path", { "d": "M12 3v18", "key": "svg-0" }], ["path", { "d": "M9 18l3 3l3 -3", "key": "svg-1" }], ["path", { "d": "M9 3h6", "key": "svg-2" }]]);

export {
  IconArrowDownBar
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconArrowDownBar.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZX2UWAEV.js.map
