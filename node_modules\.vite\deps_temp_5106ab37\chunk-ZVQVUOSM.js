import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconNavigationCode.mjs
var IconNavigationCode = createReactComponent("outline", "navigation-code", "IconNavigationCode", [["path", { "d": "M16.653 13.086l-4.653 -10.086l-7.97 17.275c-.07 .2 -.017 .424 .135 .572c.15 .148 .374 .193 .57 .116l6.246 -2.117", "key": "svg-0" }], ["path", { "d": "M20 21l2 -2l-2 -2", "key": "svg-1" }], ["path", { "d": "M17 17l-2 2l2 2", "key": "svg-2" }]]);

export {
  IconNavigationCode
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconNavigationCode.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZVQVUOSM.js.map
