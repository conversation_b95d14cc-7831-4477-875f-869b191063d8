import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconPercentage70.mjs
var IconPercentage70 = createReactComponent("outline", "percentage-70", "IconPercentage70", [["path", { "d": "M12 3a9 9 0 1 1 -8.495 11.973l8.495 -2.973z", "fill": "currentColor", "stroke": "none", "key": "svg-0" }], ["path", { "d": "M3 12a9 9 0 1 0 18 0a9 9 0 0 0 -18 0", "key": "svg-1" }]]);

export {
  IconPercentage70
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconPercentage70.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZW26NA3G.js.map
