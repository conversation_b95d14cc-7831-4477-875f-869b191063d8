import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  Paper,
  TextInput,
  Group,
  Stack,
  Card,
  Text,
  Badge,
  Image,
  Button,
  Select,
  NumberInput,
  ActionIcon,
  Tooltip,
  Loader,
  Center,
  Alert,
  Divider,
  Grid,
  Autocomplete,
} from '@mantine/core';
import {
  IconSearch,
  IconScan,
  IconFilter,
  IconX,
  IconEye,
  IconEdit,
  IconShoppingCart,
  IconBarcode,
  IconPhoto,
  IconTags,
  IconCurrency,
} from '@tabler/icons-react';
import { useDebouncedValue } from '@mantine/hooks';
import { FixedSizeList as List } from 'react-window';
import { productSearchService, ProductSearchResult, SearchFilters } from '../../services/productSearchService';
import { barcodeScannerService } from '../../services/barcodeScannerService';
import { unitConversionService } from '../../services/unitConversionService';

interface ProductSearchProps {
  onProductSelect?: (product: ProductSearchResult) => void;
  onProductView?: (product: ProductSearchResult) => void;
  onProductEdit?: (product: ProductSearchResult) => void;
  multiSelect?: boolean;
  selectedProducts?: string[];
  showActions?: boolean;
  compactView?: boolean;
}

export function ProductSearch({
  onProductSelect,
  onProductView,
  onProductEdit,
  multiSelect = false,
  selectedProducts = [],
  showActions = true,
  compactView = false,
}: ProductSearchProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedQuery] = useDebouncedValue(searchQuery, 300);
  const [filters, setFilters] = useState<SearchFilters>({});
  const [results, setResults] = useState<ProductSearchResult[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [popularSearches, setPopularSearches] = useState<string[]>([]);
  const [isScanning, setIsScanning] = useState(false);

  // Load popular searches on mount
  useEffect(() => {
    loadPopularSearches();
  }, []);

  // Search when query or filters change
  useEffect(() => {
    if (debouncedQuery || Object.keys(filters).length > 0) {
      performSearch(true);
    } else {
      setResults([]);
      setTotal(0);
    }
  }, [debouncedQuery, filters]);

  // Load search suggestions when typing
  useEffect(() => {
    if (searchQuery.length >= 2) {
      loadSuggestions(searchQuery);
    } else {
      setSuggestions([]);
    }
  }, [searchQuery]);

  const loadPopularSearches = async () => {
    try {
      const popular = await productSearchService.getPopularSearches(8);
      setPopularSearches(popular);
    } catch (error) {
      console.error('Failed to load popular searches:', error);
    }
  };

  const loadSuggestions = async (query: string) => {
    try {
      const suggestions = await productSearchService.getSearchSuggestions(query, 5);
      setSuggestions(suggestions);
    } catch (error) {
      console.error('Failed to load suggestions:', error);
    }
  };

  const performSearch = async (reset = false) => {
    setLoading(true);
    
    try {
      const searchFilters: SearchFilters = {
        ...filters,
        query: debouncedQuery || undefined,
      };

      const currentPage = reset ? 1 : page;
      const response = await productSearchService.search(searchFilters, {
        page: currentPage,
        limit: 20,
        sortBy: 'relevance',
      });

      if (reset) {
        setResults(response.results);
        setPage(1);
      } else {
        setResults(prev => [...prev, ...response.results]);
      }

      setTotal(response.total);
      setHasMore(response.hasMore);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMore = () => {
    if (!loading && hasMore) {
      setPage(prev => prev + 1);
      performSearch(false);
    }
  };

  const handleFilterChange = (key: keyof SearchFilters, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({});
    setSearchQuery('');
  };

  const handleBarcodeScanning = async () => {
    if (isScanning) {
      barcodeScannerService.stopScanning();
      setIsScanning(false);
      return;
    }

    try {
      setIsScanning(true);
      
      barcodeScannerService.onScan((result) => {
        setSearchQuery(result.code);
        setIsScanning(false);
        barcodeScannerService.stopScanning();
      });

      barcodeScannerService.onError((error) => {
        console.error('Barcode scanning error:', error);
        setIsScanning(false);
      });

      await barcodeScannerService.startScanning();
    } catch (error) {
      console.error('Failed to start barcode scanning:', error);
      setIsScanning(false);
    }
  };

  const ProductCard = ({ product, style }: { product: ProductSearchResult; style?: React.CSSProperties }) => (
    <div style={style}>
      <Card withBorder p={compactView ? 'xs' : 'md'} mb="xs">
        <Group gap="md" wrap="nowrap">
          {/* Product Image */}
          <div style={{ minWidth: compactView ? 60 : 80 }}>
            {product.images.length > 0 ? (
              <Image
                src={product.images[0]}
                alt={product.nameAr}
                width={compactView ? 60 : 80}
                height={compactView ? 60 : 80}
                radius="md"
                fallbackSrc="/images/no-image.png"
              />
            ) : (
              <Center
                style={{
                  width: compactView ? 60 : 80,
                  height: compactView ? 60 : 80,
                  backgroundColor: '#f8f9fa',
                  borderRadius: 8,
                }}
              >
                <IconPhoto size="1.5rem" color="#adb5bd" />
              </Center>
            )}
          </div>

          {/* Product Info */}
          <div style={{ flex: 1, minWidth: 0 }}>
            <Group justify="space-between" mb="xs">
              <div>
                <Text fw={500} size={compactView ? 'sm' : 'md'} lineClamp={1}>
                  {product.nameAr}
                </Text>
                <Text size="xs" c="dimmed" lineClamp={1}>
                  {product.name}
                </Text>
              </div>
              
              {!compactView && (
                <Group gap="xs">
                  <Badge color={product.currentStock > product.minStock ? 'green' : 
                               product.currentStock > 0 ? 'yellow' : 'red'} 
                         variant="light" size="sm">
                    {product.currentStock} {unitConversionService.formatUnit(product.baseUnit)}
                  </Badge>
                  {!product.isActive && (
                    <Badge color="gray" variant="light" size="sm">غير نشط</Badge>
                  )}
                </Group>
              )}
            </Group>

            <Grid gutter="xs">
              <Grid.Col span={compactView ? 12 : 6}>
                <Group gap="xs">
                  <IconBarcode size="0.9rem" color="#868e96" />
                  <Text size="xs" c="dimmed">{product.sku}</Text>
                  {product.barcode && (
                    <>
                      <Text size="xs" c="dimmed">•</Text>
                      <Text size="xs" c="dimmed">{product.barcode}</Text>
                    </>
                  )}
                </Group>
              </Grid.Col>
              
              {!compactView && (
                <Grid.Col span={6}>
                  <Group gap="xs">
                    <IconCurrency size="0.9rem" color="#868e96" />
                    <Text size="xs" fw={500}>
                      {product.sellingPrice.toLocaleString('ar-SA')} ر.س
                    </Text>
                  </Group>
                </Grid.Col>
              )}
            </Grid>

            {!compactView && (product.brand || product.model) && (
              <Group gap="xs" mt="xs">
                <IconTags size="0.9rem" color="#868e96" />
                <Text size="xs" c="dimmed">
                  {product.brandAr || product.brand}
                  {product.model && ` - ${product.model}`}
                </Text>
              </Group>
            )}

            {!compactView && product.oemNumbers.length > 0 && (
              <Group gap="xs" mt="xs">
                <Text size="xs" c="dimmed">
                  OEM: {product.oemNumbers.slice(0, 3).join(', ')}
                  {product.oemNumbers.length > 3 && ` +${product.oemNumbers.length - 3}`}
                </Text>
              </Group>
            )}
          </div>

          {/* Actions */}
          {showActions && (
            <Group gap="xs">
              {onProductView && (
                <Tooltip label="عرض التفاصيل">
                  <ActionIcon variant="light" onClick={() => onProductView(product)}>
                    <IconEye size="1rem" />
                  </ActionIcon>
                </Tooltip>
              )}
              
              {onProductEdit && (
                <Tooltip label="تعديل">
                  <ActionIcon variant="light" color="blue" onClick={() => onProductEdit(product)}>
                    <IconEdit size="1rem" />
                  </ActionIcon>
                </Tooltip>
              )}
              
              {onProductSelect && (
                <Tooltip label={multiSelect ? "إضافة للقائمة" : "اختيار"}>
                  <ActionIcon 
                    variant="light" 
                    color="green" 
                    onClick={() => onProductSelect(product)}
                  >
                    <IconShoppingCart size="1rem" />
                  </ActionIcon>
                </Tooltip>
              )}
            </Group>
          )}
        </Group>
      </Card>
    </div>
  );

  const VirtualizedList = useMemo(() => {
    if (results.length === 0) return null;

    const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => {
      const product = results[index];
      
      // Load more when near the end
      if (index === results.length - 5 && hasMore && !loading) {
        loadMore();
      }

      return <ProductCard product={product} style={style} />;
    };

    return (
      <List
        height={600}
        itemCount={results.length}
        itemSize={compactView ? 100 : 140}
        width="100%"
      >
        {Row}
      </List>
    );
  }, [results, hasMore, loading, compactView]);

  return (
    <Stack gap="md">
      {/* Search Header */}
      <Paper withBorder p="md">
        <Stack gap="md">
          {/* Main Search */}
          <Group gap="md">
            <Autocomplete
              placeholder="ابحث عن المنتجات بالاسم، رقم القطعة، الباركود، أو رقم OEM..."
              value={searchQuery}
              onChange={setSearchQuery}
              data={suggestions}
              leftSection={<IconSearch size="1rem" />}
              rightSection={
                <Group gap="xs">
                  {searchQuery && (
                    <ActionIcon variant="subtle" onClick={() => setSearchQuery('')}>
                      <IconX size="1rem" />
                    </ActionIcon>
                  )}
                  <ActionIcon 
                    variant="light" 
                    color={isScanning ? 'red' : 'blue'}
                    onClick={handleBarcodeScanning}
                    loading={isScanning}
                  >
                    <IconScan size="1rem" />
                  </ActionIcon>
                </Group>
              }
              style={{ flex: 1 }}
            />
            
            <Button
              variant="light"
              leftSection={<IconFilter size="1rem" />}
              onClick={() => setShowFilters(!showFilters)}
            >
              فلترة
            </Button>
          </Group>

          {/* Popular Searches */}
          {!searchQuery && popularSearches.length > 0 && (
            <Group gap="xs">
              <Text size="sm" c="dimmed">البحث الشائع:</Text>
              {popularSearches.map((search, index) => (
                <Button
                  key={index}
                  variant="subtle"
                  size="xs"
                  onClick={() => setSearchQuery(search)}
                >
                  {search}
                </Button>
              ))}
            </Group>
          )}

          {/* Filters */}
          {showFilters && (
            <>
              <Divider />
              <Grid>
                <Grid.Col span={3}>
                  <Select
                    label="الفئة"
                    placeholder="جميع الفئات"
                    data={[]} // TODO: Load from categories
                    value={filters.categoryId}
                    onChange={(value) => handleFilterChange('categoryId', value)}
                    clearable
                  />
                </Grid.Col>
                
                <Grid.Col span={3}>
                  <Select
                    label="حالة المخزون"
                    placeholder="جميع الحالات"
                    data={[
                      { value: 'in_stock', label: 'متوفر' },
                      { value: 'low_stock', label: 'مخزون منخفض' },
                      { value: 'out_of_stock', label: 'غير متوفر' },
                    ]}
                    value={filters.stockStatus}
                    onChange={(value) => handleFilterChange('stockStatus', value)}
                    clearable
                  />
                </Grid.Col>
                
                <Grid.Col span={3}>
                  <NumberInput
                    label="السعر من"
                    placeholder="0"
                    value={filters.priceMin}
                    onChange={(value) => handleFilterChange('priceMin', value)}
                    min={0}
                  />
                </Grid.Col>
                
                <Grid.Col span={3}>
                  <NumberInput
                    label="السعر إلى"
                    placeholder="∞"
                    value={filters.priceMax}
                    onChange={(value) => handleFilterChange('priceMax', value)}
                    min={0}
                  />
                </Grid.Col>
              </Grid>
              
              <Group justify="flex-end">
                <Button variant="subtle" onClick={clearFilters}>
                  مسح الفلاتر
                </Button>
              </Group>
            </>
          )}
        </Stack>
      </Paper>

      {/* Results */}
      <Paper withBorder p="md">
        {/* Results Header */}
        <Group justify="space-between" mb="md">
          <Text fw={500}>
            {total > 0 ? `${total.toLocaleString('ar-SA')} منتج` : 'لا توجد نتائج'}
          </Text>
          
          {loading && (
            <Group gap="xs">
              <Loader size="sm" />
              <Text size="sm" c="dimmed">جاري البحث...</Text>
            </Group>
          )}
        </Group>

        {/* Results List */}
        {results.length > 0 ? (
          VirtualizedList
        ) : !loading && (searchQuery || Object.keys(filters).length > 0) ? (
          <Alert color="blue" title="لا توجد نتائج">
            لم يتم العثور على منتجات تطابق معايير البحث. جرب تعديل كلمات البحث أو الفلاتر.
          </Alert>
        ) : (
          <Center py="xl">
            <Stack align="center" gap="md">
              <IconSearch size="3rem" color="#adb5bd" />
              <Text c="dimmed" ta="center">
                ابدأ بكتابة اسم المنتج أو رقم القطعة للبحث
              </Text>
            </Stack>
          </Center>
        )}
      </Paper>
    </Stack>
  );
}
