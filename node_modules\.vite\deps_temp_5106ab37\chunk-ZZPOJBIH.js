import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconBackground.mjs
var IconBackground = createReactComponent("outline", "background", "IconBackground", [["path", { "d": "M4 8l4 -4", "key": "svg-0" }], ["path", { "d": "M14 4l-10 10", "key": "svg-1" }], ["path", { "d": "M4 20l16 -16", "key": "svg-2" }], ["path", { "d": "M20 10l-10 10", "key": "svg-3" }], ["path", { "d": "M20 16l-4 4", "key": "svg-4" }]]);

export {
  IconBackground
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconBackground.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZZPOJBIH.js.map
