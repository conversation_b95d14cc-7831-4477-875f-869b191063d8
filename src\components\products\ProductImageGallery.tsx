import React, { useState, useCallback, useRef, useEffect } from 'react';
import {
  Paper,
  Group,
  Stack,
  ActionIcon,
  Image,
  Modal,
  Button,
  Text,
  Progress,
  Alert,
  Grid,
  Card,
  Tooltip,
  FileInput,
  Center,
  Loader,
} from '@mantine/core';
import {
  IconPhoto,
  IconTrash,
  IconDownload,
  IconMaximize,
  IconChevronLeft,
  IconChevronRight,
  IconUpload,
  IconX,
  IconCheck,
  IconAlertTriangle,
} from '@tabler/icons-react';
import { notifications } from '@mantine/notifications';

interface ProductImage {
  id: string;
  url: string;
  thumbnailUrl?: string;
  originalUrl?: string;
  filename: string;
  size: number;
  width?: number;
  height?: number;
  alt?: string;
  isPrimary?: boolean;
}

interface ProductImageGalleryProps {
  images: ProductImage[];
  onImagesChange?: (images: ProductImage[]) => void;
  onImageUpload?: (files: File[]) => Promise<ProductImage[]>;
  onImageDelete?: (imageId: string) => Promise<void>;
  maxImages?: number;
  maxFileSize?: number; // in MB
  allowedTypes?: string[];
  showUpload?: boolean;
  showDelete?: boolean;
  compressImages?: boolean;
  lazyLoading?: boolean;
}

interface ImageCompressionOptions {
  maxWidth: number;
  maxHeight: number;
  quality: number;
  format: 'jpeg' | 'webp' | 'png';
}

export function ProductImageGallery({
  images,
  onImagesChange,
  onImageUpload,
  onImageDelete,
  maxImages = 10,
  maxFileSize = 5,
  allowedTypes = ['image/jpeg', 'image/png', 'image/webp'],
  showUpload = true,
  showDelete = true,
  compressImages = true,
  lazyLoading = true,
}: ProductImageGalleryProps) {
  const [selectedImage, setSelectedImage] = useState<ProductImage | null>(null);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [loadedImages, setLoadedImages] = useState<Set<string>>(new Set());
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Image compression utility
  const compressImage = useCallback(async (
    file: File,
    options: ImageCompressionOptions
  ): Promise<File> => {
    return new Promise((resolve, reject) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();

      img.onload = () => {
        // Calculate new dimensions
        let { width, height } = img;
        const aspectRatio = width / height;

        if (width > options.maxWidth) {
          width = options.maxWidth;
          height = width / aspectRatio;
        }

        if (height > options.maxHeight) {
          height = options.maxHeight;
          width = height * aspectRatio;
        }

        // Set canvas dimensions
        canvas.width = width;
        canvas.height = height;

        // Draw and compress
        ctx?.drawImage(img, 0, 0, width, height);

        canvas.toBlob(
          (blob) => {
            if (blob) {
              const compressedFile = new File([blob], file.name, {
                type: `image/${options.format}`,
                lastModified: Date.now(),
              });
              resolve(compressedFile);
            } else {
              reject(new Error('Failed to compress image'));
            }
          },
          `image/${options.format}`,
          options.quality
        );
      };

      img.onerror = () => reject(new Error('Failed to load image'));
      img.src = URL.createObjectURL(file);
    });
  }, []);

  // Handle file upload
  const handleFileUpload = useCallback(async (files: File[]) => {
    if (!files.length) return;

    // Validate file count
    if (images.length + files.length > maxImages) {
      notifications.show({
        title: 'تجاوز الحد الأقصى',
        message: `يمكن رفع ${maxImages} صور كحد أقصى`,
        color: 'red',
        icon: <IconAlertTriangle size="1rem" />,
      });
      return;
    }

    setUploading(true);
    setUploadProgress(0);

    try {
      const processedFiles: File[] = [];

      // Process each file
      for (let i = 0; i < files.length; i++) {
        const file = files[i];

        // Validate file type
        if (!allowedTypes.includes(file.type)) {
          notifications.show({
            title: 'نوع ملف غير مدعوم',
            message: `الملف ${file.name} غير مدعوم`,
            color: 'red',
          });
          continue;
        }

        // Validate file size
        if (file.size > maxFileSize * 1024 * 1024) {
          notifications.show({
            title: 'حجم الملف كبير',
            message: `الملف ${file.name} يتجاوز ${maxFileSize}MB`,
            color: 'red',
          });
          continue;
        }

        // Compress image if enabled
        let processedFile = file;
        if (compressImages) {
          try {
            processedFile = await compressImage(file, {
              maxWidth: 1200,
              maxHeight: 1200,
              quality: 0.8,
              format: 'jpeg',
            });
          } catch (error) {
            console.warn('Failed to compress image:', error);
            // Use original file if compression fails
          }
        }

        processedFiles.push(processedFile);
        setUploadProgress(((i + 1) / files.length) * 100);
      }

      // Upload files
      if (processedFiles.length > 0 && onImageUpload) {
        const uploadedImages = await onImageUpload(processedFiles);
        
        notifications.show({
          title: 'تم رفع الصور',
          message: `تم رفع ${uploadedImages.length} صورة بنجاح`,
          color: 'green',
          icon: <IconCheck size="1rem" />,
        });
      }
    } catch (error) {
      console.error('Upload failed:', error);
      notifications.show({
        title: 'فشل في رفع الصور',
        message: 'حدث خطأ أثناء رفع الصور',
        color: 'red',
        icon: <IconX size="1rem" />,
      });
    } finally {
      setUploading(false);
      setUploadProgress(0);
    }
  }, [images.length, maxImages, maxFileSize, allowedTypes, compressImages, compressImage, onImageUpload]);

  // Handle image deletion
  const handleImageDelete = async (imageId: string) => {
    try {
      if (onImageDelete) {
        await onImageDelete(imageId);
      } else if (onImagesChange) {
        const updatedImages = images.filter(img => img.id !== imageId);
        onImagesChange(updatedImages);
      }

      notifications.show({
        title: 'تم حذف الصورة',
        message: 'تم حذف الصورة بنجاح',
        color: 'blue',
      });
    } catch (error) {
      console.error('Delete failed:', error);
      notifications.show({
        title: 'فشل في حذف الصورة',
        message: 'حدث خطأ أثناء حذف الصورة',
        color: 'red',
      });
    }
  };

  // Handle image click
  const handleImageClick = (image: ProductImage, index: number) => {
    setSelectedImage(image);
    setCurrentImageIndex(index);
  };

  // Navigate gallery
  const navigateGallery = (direction: 'prev' | 'next') => {
    const newIndex = direction === 'prev' 
      ? (currentImageIndex - 1 + images.length) % images.length
      : (currentImageIndex + 1) % images.length;
    
    setCurrentImageIndex(newIndex);
    setSelectedImage(images[newIndex]);
  };

  // Lazy loading intersection observer
  useEffect(() => {
    if (!lazyLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const imageId = entry.target.getAttribute('data-image-id');
            if (imageId) {
              setLoadedImages(prev => new Set([...prev, imageId]));
            }
          }
        });
      },
      { threshold: 0.1 }
    );

    const imageElements = document.querySelectorAll('[data-image-id]');
    imageElements.forEach(el => observer.observe(el));

    return () => observer.disconnect();
  }, [images, lazyLoading]);

  const LazyImage = ({ image, ...props }: { image: ProductImage; [key: string]: any }) => {
    const [loaded, setLoaded] = useState(!lazyLoading);
    const shouldLoad = !lazyLoading || loadedImages.has(image.id);

    return (
      <div data-image-id={image.id} style={{ position: 'relative' }}>
        {shouldLoad ? (
          <Image
            {...props}
            src={image.thumbnailUrl || image.url}
            alt={image.alt || image.filename}
            onLoad={() => setLoaded(true)}
            style={{
              opacity: loaded ? 1 : 0,
              transition: 'opacity 0.3s ease',
            }}
          />
        ) : (
          <Center style={{ height: props.height || 120, backgroundColor: '#f8f9fa' }}>
            <Loader size="sm" />
          </Center>
        )}
      </div>
    );
  };

  return (
    <Stack gap="md">
      {/* Upload Section */}
      {showUpload && (
        <Paper withBorder p="md">
          <Group justify="space-between" mb="md">
            <Text fw={500}>صور المنتج</Text>
            <Text size="sm" c="dimmed">
              {images.length} / {maxImages} صور
            </Text>
          </Group>

          {uploading && (
            <Stack gap="xs" mb="md">
              <Text size="sm">جاري رفع الصور...</Text>
              <Progress value={uploadProgress} />
            </Stack>
          )}

          <FileInput
            ref={fileInputRef}
            placeholder="اختر الصور لرفعها"
            multiple
            accept={allowedTypes.join(',')}
            onChange={handleFileUpload}
            disabled={uploading || images.length >= maxImages}
            leftSection={<IconUpload size="1rem" />}
          />

          <Text size="xs" c="dimmed" mt="xs">
            الحد الأقصى: {maxFileSize}MB لكل صورة. الأنواع المدعومة: JPG, PNG, WebP
          </Text>
        </Paper>
      )}

      {/* Images Grid */}
      {images.length > 0 ? (
        <Grid>
          {images.map((image, index) => (
            <Grid.Col key={image.id} span={3}>
              <Card withBorder p="xs" style={{ position: 'relative' }}>
                <LazyImage
                  image={image}
                  height={120}
                  radius="md"
                  style={{ cursor: 'pointer' }}
                  onClick={() => handleImageClick(image, index)}
                />

                {/* Image Actions */}
                <Group justify="space-between" mt="xs">
                  <Group gap="xs">
                    {image.isPrimary && (
                      <Text size="xs" c="blue" fw={500}>
                        رئيسية
                      </Text>
                    )}
                  </Group>

                  <Group gap="xs">
                    <Tooltip label="عرض بالحجم الكامل">
                      <ActionIcon
                        size="sm"
                        variant="light"
                        onClick={() => handleImageClick(image, index)}
                      >
                        <IconMaximize size="0.8rem" />
                      </ActionIcon>
                    </Tooltip>

                    {showDelete && (
                      <Tooltip label="حذف الصورة">
                        <ActionIcon
                          size="sm"
                          variant="light"
                          color="red"
                          onClick={() => handleImageDelete(image.id)}
                        >
                          <IconTrash size="0.8rem" />
                        </ActionIcon>
                      </Tooltip>
                    )}
                  </Group>
                </Group>
              </Card>
            </Grid.Col>
          ))}
        </Grid>
      ) : (
        <Alert color="blue" title="لا توجد صور">
          لم يتم رفع أي صور بعد. استخدم زر الرفع لإضافة صور المنتج.
        </Alert>
      )}

      {/* Image Viewer Modal */}
      <Modal
        opened={!!selectedImage}
        onClose={() => setSelectedImage(null)}
        size="xl"
        title={selectedImage?.filename}
        centered
      >
        {selectedImage && (
          <Stack gap="md">
            <div style={{ position: 'relative' }}>
              <Image
                src={selectedImage.originalUrl || selectedImage.url}
                alt={selectedImage.alt || selectedImage.filename}
                radius="md"
                style={{ maxHeight: '70vh' }}
              />

              {/* Navigation Arrows */}
              {images.length > 1 && (
                <>
                  <ActionIcon
                    size="lg"
                    variant="filled"
                    style={{
                      position: 'absolute',
                      left: 10,
                      top: '50%',
                      transform: 'translateY(-50%)',
                    }}
                    onClick={() => navigateGallery('prev')}
                  >
                    <IconChevronLeft size="1.2rem" />
                  </ActionIcon>

                  <ActionIcon
                    size="lg"
                    variant="filled"
                    style={{
                      position: 'absolute',
                      right: 10,
                      top: '50%',
                      transform: 'translateY(-50%)',
                    }}
                    onClick={() => navigateGallery('next')}
                  >
                    <IconChevronRight size="1.2rem" />
                  </ActionIcon>
                </>
              )}
            </div>

            {/* Image Info */}
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed">
                  {selectedImage.width && selectedImage.height && 
                    `${selectedImage.width} × ${selectedImage.height} بكسل`
                  }
                  {selectedImage.size && 
                    ` • ${(selectedImage.size / 1024 / 1024).toFixed(2)} MB`
                  }
                </Text>
              </div>

              <Group gap="xs">
                <Button
                  variant="light"
                  leftSection={<IconDownload size="1rem" />}
                  onClick={() => {
                    const link = document.createElement('a');
                    link.href = selectedImage.originalUrl || selectedImage.url;
                    link.download = selectedImage.filename;
                    link.click();
                  }}
                >
                  تحميل
                </Button>
              </Group>
            </Group>
          </Stack>
        )}
      </Modal>
    </Stack>
  );
}
