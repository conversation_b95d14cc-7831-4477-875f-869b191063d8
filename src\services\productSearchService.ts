// Advanced product search service with real-time search and virtual scrolling

export interface ProductSearchResult {
  id: string;
  name: string;
  nameAr: string;
  sku: string;
  barcode?: string;
  brand?: string;
  brandAr?: string;
  model?: string;
  categoryName?: string;
  categoryNameAr?: string;
  images: string[];
  baseUnit: string;
  sellingPrice: number;
  costPrice: number;
  currentStock: number;
  minStock: number;
  oemNumbers: string[];
  suppliers: string[];
  isActive: boolean;
  relevanceScore?: number;
}

export interface SearchFilters {
  query?: string;
  categoryId?: string;
  brandId?: string;
  priceMin?: number;
  priceMax?: number;
  stockStatus?: 'all' | 'in_stock' | 'low_stock' | 'out_of_stock';
  isActive?: boolean;
  hasImages?: boolean;
  supplierId?: string;
  tags?: string[];
}

export interface SearchOptions {
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'price' | 'stock' | 'created' | 'relevance';
  sortOrder?: 'asc' | 'desc';
  includeInactive?: boolean;
}

export interface SearchResponse {
  results: ProductSearchResult[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasMore: boolean;
  searchTime: number;
}

export class ProductSearchService {
  private static instance: ProductSearchService;
  private searchCache: Map<string, SearchResponse> = new Map();
  private searchIndex: Map<string, Set<string>> = new Map();
  private lastIndexUpdate: Date = new Date(0);

  private constructor() {}

  static getInstance(): ProductSearchService {
    if (!ProductSearchService.instance) {
      ProductSearchService.instance = new ProductSearchService();
    }
    return ProductSearchService.instance;
  }

  // Main search method with real-time capabilities
  async search(
    filters: SearchFilters = {},
    options: SearchOptions = {}
  ): Promise<SearchResponse> {
    const startTime = Date.now();
    
    // Generate cache key
    const cacheKey = this.generateCacheKey(filters, options);
    
    // Check cache first
    const cached = this.searchCache.get(cacheKey);
    if (cached && this.isCacheValid(cached)) {
      return {
        ...cached,
        searchTime: Date.now() - startTime,
      };
    }

    try {
      // Perform search
      const response = await this.performSearch(filters, options);
      
      // Cache results
      this.searchCache.set(cacheKey, response);
      
      return {
        ...response,
        searchTime: Date.now() - startTime,
      };
    } catch (error) {
      console.error('Search failed:', error);
      throw error;
    }
  }

  // Perform the actual search
  private async performSearch(
    filters: SearchFilters,
    options: SearchOptions
  ): Promise<SearchResponse> {
    const {
      page = 1,
      limit = 50,
      sortBy = 'relevance',
      sortOrder = 'desc',
      includeInactive = false,
    } = options;

    // Build SQL query
    let sql = `
      SELECT 
        p.id,
        p.name,
        p.name_ar as nameAr,
        p.sku,
        p.barcode,
        p.brand,
        p.brand_ar as brandAr,
        p.model,
        p.images,
        p.selling_price as sellingPrice,
        p.cost_price as costPrice,
        p.min_stock as minStock,
        p.is_active as isActive,
        pc.name as categoryName,
        pc.name_ar as categoryNameAr,
        u.symbol as baseUnit,
        COALESCE(inv.total_stock, 0) as currentStock,
        GROUP_CONCAT(DISTINCT pon.oem_number) as oemNumbers,
        GROUP_CONCAT(DISTINCT s.name) as suppliers
      FROM products p
      LEFT JOIN product_categories pc ON p.category_id = pc.id
      LEFT JOIN units u ON p.base_unit_id = u.id
      LEFT JOIN (
        SELECT product_id, SUM(quantity) as total_stock
        FROM inventory
        GROUP BY product_id
      ) inv ON p.id = inv.product_id
      LEFT JOIN product_oem_numbers pon ON p.id = pon.product_id
      LEFT JOIN product_suppliers ps ON p.id = ps.product_id AND ps.is_active = 1
      LEFT JOIN suppliers s ON ps.supplier_id = s.id
      WHERE 1=1
    `;

    const params: any[] = [];

    // Apply filters
    if (!includeInactive) {
      sql += ' AND p.is_active = 1';
    }

    if (filters.query) {
      const searchTerms = this.parseSearchQuery(filters.query);
      const searchConditions: string[] = [];
      
      searchTerms.forEach(term => {
        searchConditions.push(`(
          p.name LIKE ? OR 
          p.name_ar LIKE ? OR 
          p.sku LIKE ? OR 
          p.barcode LIKE ? OR 
          p.brand LIKE ? OR 
          p.brand_ar LIKE ? OR 
          p.model LIKE ? OR
          pon.oem_number LIKE ?
        )`);
        
        const likeTerm = `%${term}%`;
        params.push(likeTerm, likeTerm, likeTerm, likeTerm, likeTerm, likeTerm, likeTerm, likeTerm);
      });
      
      if (searchConditions.length > 0) {
        sql += ` AND (${searchConditions.join(' AND ')})`;
      }
    }

    if (filters.categoryId) {
      sql += ' AND p.category_id = ?';
      params.push(filters.categoryId);
    }

    if (filters.priceMin !== undefined) {
      sql += ' AND p.selling_price >= ?';
      params.push(filters.priceMin);
    }

    if (filters.priceMax !== undefined) {
      sql += ' AND p.selling_price <= ?';
      params.push(filters.priceMax);
    }

    if (filters.stockStatus) {
      switch (filters.stockStatus) {
        case 'in_stock':
          sql += ' AND COALESCE(inv.total_stock, 0) > 0';
          break;
        case 'low_stock':
          sql += ' AND COALESCE(inv.total_stock, 0) <= p.min_stock AND COALESCE(inv.total_stock, 0) > 0';
          break;
        case 'out_of_stock':
          sql += ' AND COALESCE(inv.total_stock, 0) = 0';
          break;
      }
    }

    if (filters.hasImages) {
      sql += ' AND p.images IS NOT NULL AND p.images != "[]"';
    }

    if (filters.supplierId) {
      sql += ' AND ps.supplier_id = ?';
      params.push(filters.supplierId);
    }

    // Group by product
    sql += ' GROUP BY p.id';

    // Add sorting
    switch (sortBy) {
      case 'name':
        sql += ` ORDER BY p.name_ar ${sortOrder.toUpperCase()}`;
        break;
      case 'price':
        sql += ` ORDER BY p.selling_price ${sortOrder.toUpperCase()}`;
        break;
      case 'stock':
        sql += ` ORDER BY currentStock ${sortOrder.toUpperCase()}`;
        break;
      case 'created':
        sql += ` ORDER BY p.created_at ${sortOrder.toUpperCase()}`;
        break;
      case 'relevance':
      default:
        if (filters.query) {
          // Calculate relevance score for text search
          sql += ` ORDER BY (
            CASE 
              WHEN p.name_ar LIKE ? THEN 100
              WHEN p.name LIKE ? THEN 90
              WHEN p.sku LIKE ? THEN 80
              WHEN p.barcode LIKE ? THEN 70
              WHEN pon.oem_number LIKE ? THEN 60
              ELSE 50
            END
          ) DESC`;
          const exactTerm = `%${filters.query}%`;
          params.push(exactTerm, exactTerm, exactTerm, exactTerm, exactTerm);
        } else {
          sql += ` ORDER BY p.name_ar ASC`;
        }
        break;
    }

    // Add pagination
    const offset = (page - 1) * limit;
    sql += ` LIMIT ? OFFSET ?`;
    params.push(limit, offset);

    // Execute search query
    const results = await this.executeQuery(sql, params);

    // Get total count
    const countSql = sql.replace(/SELECT.*?FROM/, 'SELECT COUNT(DISTINCT p.id) as total FROM')
                        .replace(/GROUP BY.*?ORDER BY.*?LIMIT.*?OFFSET.*?$/, '');
    const countParams = params.slice(0, -2); // Remove limit and offset
    const countResult = await this.executeQuery(countSql, countParams);
    const total = countResult[0]?.total || 0;

    // Process results
    const processedResults = results.map(row => this.processSearchResult(row));

    return {
      results: processedResults,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      hasMore: page * limit < total,
      searchTime: 0, // Will be set by caller
    };
  }

  // Parse search query into terms
  private parseSearchQuery(query: string): string[] {
    return query
      .trim()
      .split(/\s+/)
      .filter(term => term.length > 0)
      .map(term => term.toLowerCase());
  }

  // Process search result row
  private processSearchResult(row: any): ProductSearchResult {
    return {
      id: row.id,
      name: row.name,
      nameAr: row.nameAr,
      sku: row.sku,
      barcode: row.barcode,
      brand: row.brand,
      brandAr: row.brandAr,
      model: row.model,
      categoryName: row.categoryName,
      categoryNameAr: row.categoryNameAr,
      images: row.images ? JSON.parse(row.images) : [],
      baseUnit: row.baseUnit,
      sellingPrice: row.sellingPrice,
      costPrice: row.costPrice,
      currentStock: row.currentStock,
      minStock: row.minStock,
      oemNumbers: row.oemNumbers ? row.oemNumbers.split(',') : [],
      suppliers: row.suppliers ? row.suppliers.split(',') : [],
      isActive: Boolean(row.isActive),
    };
  }

  // Execute database query (mock implementation)
  private async executeQuery(sql: string, params: any[]): Promise<any[]> {
    // TODO: Replace with actual database query
    console.log('Executing query:', sql, params);
    
    // Mock data for development
    return this.generateMockResults();
  }

  // Generate mock search results
  private generateMockResults(): any[] {
    const mockResults = [];
    for (let i = 1; i <= 20; i++) {
      mockResults.push({
        id: `product-${i}`,
        name: `Product ${i}`,
        nameAr: `منتج ${i}`,
        sku: `SKU-${i.toString().padStart(3, '0')}`,
        barcode: `123456789${i.toString().padStart(3, '0')}`,
        brand: 'Toyota',
        brandAr: 'تويوتا',
        model: 'Camry',
        categoryName: 'Engine Parts',
        categoryNameAr: 'قطع المحرك',
        images: JSON.stringify(['/images/product1.jpg']),
        baseUnit: 'قطعة',
        sellingPrice: 100 + (i * 10),
        costPrice: 80 + (i * 8),
        currentStock: Math.floor(Math.random() * 100),
        minStock: 10,
        oemNumbers: `OEM-${i},ALT-${i}`,
        suppliers: 'Supplier A,Supplier B',
        isActive: 1,
      });
    }
    return mockResults;
  }

  // Generate cache key
  private generateCacheKey(filters: SearchFilters, options: SearchOptions): string {
    return JSON.stringify({ filters, options });
  }

  // Check if cache is valid (5 minutes)
  private isCacheValid(cached: SearchResponse): boolean {
    return Date.now() - cached.searchTime < 5 * 60 * 1000;
  }

  // Clear search cache
  clearCache(): void {
    this.searchCache.clear();
  }

  // Get search suggestions
  async getSearchSuggestions(query: string, limit: number = 10): Promise<string[]> {
    if (query.length < 2) {
      return [];
    }

    // TODO: Implement actual suggestions from database
    const suggestions = [
      'فلتر زيت تويوتا',
      'فرامل هوندا',
      'بطارية 70 أمبير',
      'إطار 185/65R15',
      'زيت محرك 5W30',
    ].filter(suggestion => 
      suggestion.toLowerCase().includes(query.toLowerCase())
    );

    return suggestions.slice(0, limit);
  }

  // Get popular searches
  async getPopularSearches(limit: number = 10): Promise<string[]> {
    // TODO: Implement actual popular searches from analytics
    return [
      'فلتر زيت',
      'فرامل',
      'بطارية',
      'إطارات',
      'زيت محرك',
      'شمعات احتراق',
      'فلتر هواء',
      'مساحات',
    ].slice(0, limit);
  }
}

// Export singleton instance
export const productSearchService = ProductSearchService.getInstance();
