import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconShirt.mjs
var IconShirt = createReactComponent("outline", "shirt", "IconShirt", [["path", { "d": "M15 4l6 2v5h-3v8a1 1 0 0 1 -1 1h-10a1 1 0 0 1 -1 -1v-8h-3v-5l6 -2a3 3 0 0 0 6 0", "key": "svg-0" }]]);

export {
  IconShirt
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconShirt.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZX5KFQ27.js.map
