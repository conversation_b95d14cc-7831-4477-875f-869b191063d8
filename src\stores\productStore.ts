import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { ProductFormData } from '../components/products/ProductForm';
import { ProductCategory } from '../components/products/ProductCategoriesTree';
import { ProductSearchResult, SearchFilters } from '../services/productSearchService';
import { unitConversionService } from '../services/unitConversionService';

export interface ProductImage {
  id: string;
  url: string;
  thumbnailUrl?: string;
  originalUrl?: string;
  filename: string;
  size: number;
  width?: number;
  height?: number;
  alt?: string;
  isPrimary?: boolean;
}

export interface ProductOemNumber {
  id: string;
  productId: string;
  oemNumber: string;
  manufacturer?: string;
  manufacturerAr?: string;
  isOriginal: boolean;
  notes?: string;
}

export interface ProductSupplier {
  id: string;
  productId: string;
  supplierId: string;
  supplierSku?: string;
  supplierPrice?: number;
  minimumOrderQuantity?: number;
  leadTimeDays?: number;
  isPreferred: boolean;
  isActive: boolean;
}

export interface ProductPriceHistory {
  id: string;
  productId: string;
  priceType: 'cost' | 'selling' | 'wholesale';
  oldPrice: number;
  newPrice: number;
  changeReason?: string;
  changedBy: string;
  changedAt: Date;
}

export interface ProductTemplate {
  id: string;
  name: string;
  nameAr: string;
  type: 'oil' | 'tire' | 'battery' | 'general';
  fields: Array<{
    key: string;
    label: string;
    labelAr: string;
    type: 'text' | 'number' | 'select' | 'multiselect';
    required?: boolean;
    options?: Array<{ value: string; label: string; labelAr: string }>;
    validation?: any;
  }>;
  isActive: boolean;
}

interface ProductState {
  // Products
  products: ProductSearchResult[];
  selectedProduct: ProductSearchResult | null;
  searchFilters: SearchFilters;
  searchResults: {
    results: ProductSearchResult[];
    total: number;
    page: number;
    hasMore: boolean;
    loading: boolean;
  };

  // Categories
  categories: ProductCategory[];
  selectedCategory: ProductCategory | null;

  // Templates
  templates: ProductTemplate[];
  selectedTemplate: ProductTemplate | null;

  // Images
  productImages: Record<string, ProductImage[]>;

  // OEM Numbers
  productOemNumbers: Record<string, ProductOemNumber[]>;

  // Suppliers
  productSuppliers: Record<string, ProductSupplier[]>;

  // Price History
  priceHistory: Record<string, ProductPriceHistory[]>;

  // Loading states
  loading: {
    products: boolean;
    categories: boolean;
    templates: boolean;
    saving: boolean;
  };

  // Error state
  error: string | null;

  // Actions
  // Product actions
  searchProducts: (filters: SearchFilters, page?: number) => Promise<void>;
  loadProduct: (id: string) => Promise<ProductSearchResult | null>;
  createProduct: (data: ProductFormData) => Promise<string>;
  updateProduct: (id: string, data: Partial<ProductFormData>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  setSelectedProduct: (product: ProductSearchResult | null) => void;
  clearSearchResults: () => void;

  // Category actions
  loadCategories: () => Promise<void>;
  createCategory: (category: Omit<ProductCategory, 'id'>) => Promise<string>;
  updateCategory: (id: string, updates: Partial<ProductCategory>) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;
  moveCategory: (categoryId: string, newParentId?: string, newIndex?: number) => Promise<void>;
  setSelectedCategory: (category: ProductCategory | null) => void;

  // Template actions
  loadTemplates: () => Promise<void>;
  createTemplate: (template: Omit<ProductTemplate, 'id'>) => Promise<string>;
  updateTemplate: (id: string, updates: Partial<ProductTemplate>) => Promise<void>;
  deleteTemplate: (id: string) => Promise<void>;
  setSelectedTemplate: (template: ProductTemplate | null) => void;

  // Image actions
  uploadProductImages: (productId: string, files: File[]) => Promise<ProductImage[]>;
  deleteProductImage: (productId: string, imageId: string) => Promise<void>;
  reorderProductImages: (productId: string, imageIds: string[]) => Promise<void>;

  // OEM actions
  addOemNumber: (productId: string, oemNumber: Omit<ProductOemNumber, 'id' | 'productId'>) => Promise<string>;
  updateOemNumber: (id: string, updates: Partial<ProductOemNumber>) => Promise<void>;
  deleteOemNumber: (id: string) => Promise<void>;

  // Supplier actions
  addProductSupplier: (productId: string, supplier: Omit<ProductSupplier, 'id' | 'productId'>) => Promise<string>;
  updateProductSupplier: (id: string, updates: Partial<ProductSupplier>) => Promise<void>;
  deleteProductSupplier: (id: string) => Promise<void>;

  // Price history
  addPriceChange: (productId: string, priceChange: Omit<ProductPriceHistory, 'id' | 'productId' | 'changedAt'>) => Promise<void>;
  getPriceHistory: (productId: string) => Promise<ProductPriceHistory[]>;

  // Utility actions
  generateSku: (categoryId?: string) => string;
  validateBarcode: (barcode: string) => boolean;
  calculateProfitMargin: (costPrice: number, sellingPrice: number) => number;
  getProductsByCategory: (categoryId: string) => ProductSearchResult[];
  getProductsBySupplier: (supplierId: string) => ProductSearchResult[];
  getLowStockProducts: (threshold?: number) => ProductSearchResult[];
  getOutOfStockProducts: () => ProductSearchResult[];
}

export const useProductStore = create<ProductState>()(
  persist(
    (set, get) => ({
      // Initial state
      products: [],
      selectedProduct: null,
      searchFilters: {},
      searchResults: {
        results: [],
        total: 0,
        page: 1,
        hasMore: false,
        loading: false,
      },
      categories: [],
      selectedCategory: null,
      templates: [],
      selectedTemplate: null,
      productImages: {},
      productOemNumbers: {},
      productSuppliers: {},
      priceHistory: {},
      loading: {
        products: false,
        categories: false,
        templates: false,
        saving: false,
      },
      error: null,

      // Product actions
      searchProducts: async (filters, page = 1) => {
        set(state => ({
          searchResults: { ...state.searchResults, loading: true },
          error: null,
        }));

        try {
          // TODO: Replace with actual API call
          const mockResults = await new Promise<ProductSearchResult[]>(resolve => {
            setTimeout(() => {
              resolve([
                {
                  id: 'product-1',
                  name: 'Oil Filter',
                  nameAr: 'فلتر زيت',
                  sku: 'OF-001',
                  barcode: '1234567890123',
                  brand: 'Toyota',
                  brandAr: 'تويوتا',
                  model: 'Camry',
                  categoryName: 'Filters',
                  categoryNameAr: 'الفلاتر',
                  images: ['/images/oil-filter.jpg'],
                  baseUnit: 'piece',
                  sellingPrice: 25.00,
                  costPrice: 18.00,
                  currentStock: 50,
                  minStock: 10,
                  oemNumbers: ['90915-YZZD1', 'ALT-001'],
                  suppliers: ['Toyota', 'Aftermarket'],
                  isActive: true,
                },
              ]);
            }, 500);
          });

          set(state => ({
            searchResults: {
              results: page === 1 ? mockResults : [...state.searchResults.results, ...mockResults],
              total: mockResults.length,
              page,
              hasMore: false,
              loading: false,
            },
            searchFilters: filters,
          }));
        } catch (error) {
          console.error('Search failed:', error);
          set({
            error: error instanceof Error ? error.message : 'Search failed',
            searchResults: { ...get().searchResults, loading: false },
          });
        }
      },

      loadProduct: async (id) => {
        try {
          // TODO: Replace with actual API call
          const product = get().products.find(p => p.id === id);
          return product || null;
        } catch (error) {
          console.error('Failed to load product:', error);
          return null;
        }
      },

      createProduct: async (data) => {
        set(state => ({ loading: { ...state.loading, saving: true }, error: null }));

        try {
          // TODO: Replace with actual API call
          const newId = `product-${Date.now()}`;
          
          // Create unit conversions
          const unitConversions = unitConversionService.createProductUnitConversions(
            newId,
            data.baseUnitId,
            data.sellingPrice
          );

          console.log('Creating product:', { ...data, unitConversions });

          set(state => ({ loading: { ...state.loading, saving: false } }));
          return newId;
        } catch (error) {
          console.error('Failed to create product:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to create product',
            loading: { ...get().loading, saving: false },
          });
          throw error;
        }
      },

      updateProduct: async (id, data) => {
        set(state => ({ loading: { ...state.loading, saving: true }, error: null }));

        try {
          // TODO: Replace with actual API call
          console.log('Updating product:', id, data);

          set(state => ({ loading: { ...state.loading, saving: false } }));
        } catch (error) {
          console.error('Failed to update product:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to update product',
            loading: { ...get().loading, saving: false },
          });
          throw error;
        }
      },

      deleteProduct: async (id) => {
        try {
          // TODO: Replace with actual API call
          console.log('Deleting product:', id);

          set(state => ({
            products: state.products.filter(p => p.id !== id),
            selectedProduct: state.selectedProduct?.id === id ? null : state.selectedProduct,
          }));
        } catch (error) {
          console.error('Failed to delete product:', error);
          throw error;
        }
      },

      setSelectedProduct: (product) => {
        set({ selectedProduct: product });
      },

      clearSearchResults: () => {
        set({
          searchResults: {
            results: [],
            total: 0,
            page: 1,
            hasMore: false,
            loading: false,
          },
          searchFilters: {},
        });
      },

      // Category actions
      loadCategories: async () => {
        set(state => ({ loading: { ...state.loading, categories: true }, error: null }));

        try {
          // TODO: Replace with actual API call
          const mockCategories: ProductCategory[] = [
            {
              id: 'engine',
              name: 'Engine Parts',
              nameAr: 'قطع المحرك',
              color: '#e74c3c',
              sortOrder: 1,
              isActive: true,
              productCount: 150,
            },
          ];

          set({
            categories: mockCategories,
            loading: { ...get().loading, categories: false },
          });
        } catch (error) {
          console.error('Failed to load categories:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to load categories',
            loading: { ...get().loading, categories: false },
          });
        }
      },

      createCategory: async (category) => {
        try {
          // TODO: Replace with actual API call
          const newId = `category-${Date.now()}`;
          const newCategory: ProductCategory = { ...category, id: newId };

          set(state => ({
            categories: [...state.categories, newCategory],
          }));

          return newId;
        } catch (error) {
          console.error('Failed to create category:', error);
          throw error;
        }
      },

      updateCategory: async (id, updates) => {
        try {
          // TODO: Replace with actual API call
          set(state => ({
            categories: state.categories.map(cat =>
              cat.id === id ? { ...cat, ...updates } : cat
            ),
          }));
        } catch (error) {
          console.error('Failed to update category:', error);
          throw error;
        }
      },

      deleteCategory: async (id) => {
        try {
          // TODO: Replace with actual API call
          set(state => ({
            categories: state.categories.filter(cat => cat.id !== id),
            selectedCategory: state.selectedCategory?.id === id ? null : state.selectedCategory,
          }));
        } catch (error) {
          console.error('Failed to delete category:', error);
          throw error;
        }
      },

      moveCategory: async (categoryId, newParentId, newIndex) => {
        try {
          // TODO: Implement category moving logic
          console.log('Moving category:', categoryId, 'to parent:', newParentId, 'at index:', newIndex);
        } catch (error) {
          console.error('Failed to move category:', error);
          throw error;
        }
      },

      setSelectedCategory: (category) => {
        set({ selectedCategory: category });
      },

      // Template actions
      loadTemplates: async () => {
        set(state => ({ loading: { ...state.loading, templates: true }, error: null }));

        try {
          // TODO: Replace with actual API call
          const mockTemplates: ProductTemplate[] = [];

          set({
            templates: mockTemplates,
            loading: { ...get().loading, templates: false },
          });
        } catch (error) {
          console.error('Failed to load templates:', error);
          set({
            error: error instanceof Error ? error.message : 'Failed to load templates',
            loading: { ...get().loading, templates: false },
          });
        }
      },

      createTemplate: async (template) => {
        try {
          // TODO: Replace with actual API call
          const newId = `template-${Date.now()}`;
          const newTemplate: ProductTemplate = { ...template, id: newId };

          set(state => ({
            templates: [...state.templates, newTemplate],
          }));

          return newId;
        } catch (error) {
          console.error('Failed to create template:', error);
          throw error;
        }
      },

      updateTemplate: async (id, updates) => {
        try {
          // TODO: Replace with actual API call
          set(state => ({
            templates: state.templates.map(template =>
              template.id === id ? { ...template, ...updates } : template
            ),
          }));
        } catch (error) {
          console.error('Failed to update template:', error);
          throw error;
        }
      },

      deleteTemplate: async (id) => {
        try {
          // TODO: Replace with actual API call
          set(state => ({
            templates: state.templates.filter(template => template.id !== id),
            selectedTemplate: state.selectedTemplate?.id === id ? null : state.selectedTemplate,
          }));
        } catch (error) {
          console.error('Failed to delete template:', error);
          throw error;
        }
      },

      setSelectedTemplate: (template) => {
        set({ selectedTemplate: template });
      },

      // Image actions
      uploadProductImages: async (productId, files) => {
        try {
          // TODO: Replace with actual API call
          const newImages: ProductImage[] = files.map((file, index) => ({
            id: `img-${Date.now()}-${index}`,
            url: URL.createObjectURL(file),
            filename: file.name,
            size: file.size,
            isPrimary: index === 0,
          }));

          set(state => ({
            productImages: {
              ...state.productImages,
              [productId]: [...(state.productImages[productId] || []), ...newImages],
            },
          }));

          return newImages;
        } catch (error) {
          console.error('Failed to upload images:', error);
          throw error;
        }
      },

      deleteProductImage: async (productId, imageId) => {
        try {
          // TODO: Replace with actual API call
          set(state => ({
            productImages: {
              ...state.productImages,
              [productId]: (state.productImages[productId] || []).filter(img => img.id !== imageId),
            },
          }));
        } catch (error) {
          console.error('Failed to delete image:', error);
          throw error;
        }
      },

      reorderProductImages: async (productId, imageIds) => {
        try {
          // TODO: Replace with actual API call
          const currentImages = get().productImages[productId] || [];
          const reorderedImages = imageIds.map(id => 
            currentImages.find(img => img.id === id)!
          ).filter(Boolean);

          set(state => ({
            productImages: {
              ...state.productImages,
              [productId]: reorderedImages,
            },
          }));
        } catch (error) {
          console.error('Failed to reorder images:', error);
          throw error;
        }
      },

      // OEM actions
      addOemNumber: async (productId, oemNumber) => {
        try {
          // TODO: Replace with actual API call
          const newId = `oem-${Date.now()}`;
          const newOemNumber: ProductOemNumber = { ...oemNumber, id: newId, productId };

          set(state => ({
            productOemNumbers: {
              ...state.productOemNumbers,
              [productId]: [...(state.productOemNumbers[productId] || []), newOemNumber],
            },
          }));

          return newId;
        } catch (error) {
          console.error('Failed to add OEM number:', error);
          throw error;
        }
      },

      updateOemNumber: async (id, updates) => {
        try {
          // TODO: Replace with actual API call
          set(state => {
            const newOemNumbers = { ...state.productOemNumbers };
            Object.keys(newOemNumbers).forEach(productId => {
              newOemNumbers[productId] = newOemNumbers[productId].map(oem =>
                oem.id === id ? { ...oem, ...updates } : oem
              );
            });
            return { productOemNumbers: newOemNumbers };
          });
        } catch (error) {
          console.error('Failed to update OEM number:', error);
          throw error;
        }
      },

      deleteOemNumber: async (id) => {
        try {
          // TODO: Replace with actual API call
          set(state => {
            const newOemNumbers = { ...state.productOemNumbers };
            Object.keys(newOemNumbers).forEach(productId => {
              newOemNumbers[productId] = newOemNumbers[productId].filter(oem => oem.id !== id);
            });
            return { productOemNumbers: newOemNumbers };
          });
        } catch (error) {
          console.error('Failed to delete OEM number:', error);
          throw error;
        }
      },

      // Supplier actions
      addProductSupplier: async (productId, supplier) => {
        try {
          // TODO: Replace with actual API call
          const newId = `supplier-${Date.now()}`;
          const newSupplier: ProductSupplier = { ...supplier, id: newId, productId };

          set(state => ({
            productSuppliers: {
              ...state.productSuppliers,
              [productId]: [...(state.productSuppliers[productId] || []), newSupplier],
            },
          }));

          return newId;
        } catch (error) {
          console.error('Failed to add product supplier:', error);
          throw error;
        }
      },

      updateProductSupplier: async (id, updates) => {
        try {
          // TODO: Replace with actual API call
          set(state => {
            const newSuppliers = { ...state.productSuppliers };
            Object.keys(newSuppliers).forEach(productId => {
              newSuppliers[productId] = newSuppliers[productId].map(supplier =>
                supplier.id === id ? { ...supplier, ...updates } : supplier
              );
            });
            return { productSuppliers: newSuppliers };
          });
        } catch (error) {
          console.error('Failed to update product supplier:', error);
          throw error;
        }
      },

      deleteProductSupplier: async (id) => {
        try {
          // TODO: Replace with actual API call
          set(state => {
            const newSuppliers = { ...state.productSuppliers };
            Object.keys(newSuppliers).forEach(productId => {
              newSuppliers[productId] = newSuppliers[productId].filter(supplier => supplier.id !== id);
            });
            return { productSuppliers: newSuppliers };
          });
        } catch (error) {
          console.error('Failed to delete product supplier:', error);
          throw error;
        }
      },

      // Price history
      addPriceChange: async (productId, priceChange) => {
        try {
          // TODO: Replace with actual API call
          const newPriceChange: ProductPriceHistory = {
            ...priceChange,
            id: `price-${Date.now()}`,
            productId,
            changedAt: new Date(),
          };

          set(state => ({
            priceHistory: {
              ...state.priceHistory,
              [productId]: [...(state.priceHistory[productId] || []), newPriceChange],
            },
          }));
        } catch (error) {
          console.error('Failed to add price change:', error);
          throw error;
        }
      },

      getPriceHistory: async (productId) => {
        try {
          // TODO: Replace with actual API call
          return get().priceHistory[productId] || [];
        } catch (error) {
          console.error('Failed to get price history:', error);
          return [];
        }
      },

      // Utility actions
      generateSku: (categoryId) => {
        const timestamp = Date.now().toString().slice(-6);
        const categoryPrefix = categoryId ? categoryId.substring(0, 3).toUpperCase() : 'GEN';
        return `${categoryPrefix}-${timestamp}`;
      },

      validateBarcode: (barcode) => {
        // Basic barcode validation
        return /^\d{8,13}$/.test(barcode);
      },

      calculateProfitMargin: (costPrice, sellingPrice) => {
        if (costPrice === 0) return 0;
        return ((sellingPrice - costPrice) / costPrice) * 100;
      },

      getProductsByCategory: (categoryId) => {
        return get().products.filter(product => product.categoryName === categoryId);
      },

      getProductsBySupplier: (supplierId) => {
        return get().products.filter(product => 
          product.suppliers.includes(supplierId)
        );
      },

      getLowStockProducts: (threshold = 10) => {
        return get().products.filter(product => 
          product.currentStock <= (threshold || product.minStock)
        );
      },

      getOutOfStockProducts: () => {
        return get().products.filter(product => product.currentStock === 0);
      },
    }),
    {
      name: 'product-storage',
      partialize: (state) => ({
        searchFilters: state.searchFilters,
        selectedCategory: state.selectedCategory,
        selectedTemplate: state.selectedTemplate,
      }),
    }
  )
);
