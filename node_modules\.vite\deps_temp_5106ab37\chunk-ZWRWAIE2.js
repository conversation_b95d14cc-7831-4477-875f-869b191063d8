import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconUserX.mjs
var IconUserX = createReactComponent("outline", "user-x", "IconUserX", [["path", { "d": "M8 7a4 4 0 1 0 8 0a4 4 0 0 0 -8 0", "key": "svg-0" }], ["path", { "d": "M6 21v-2a4 4 0 0 1 4 -4h3.5", "key": "svg-1" }], ["path", { "d": "M22 22l-5 -5", "key": "svg-2" }], ["path", { "d": "M17 22l5 -5", "key": "svg-3" }]]);

export {
  IconUserX
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconUserX.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZWRWAIE2.js.map
