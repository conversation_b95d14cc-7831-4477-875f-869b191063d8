// Barcode scanning service supporting camera and USB scanner input

export interface BarcodeResult {
  code: string;
  format: string;
  timestamp: Date;
  confidence?: number;
}

export interface ScannerConfig {
  enableCamera: boolean;
  enableUSB: boolean;
  autoStart: boolean;
  beepOnScan: boolean;
  vibrationOnScan: boolean;
  scanDelay: number; // Milliseconds between scans
  supportedFormats: string[];
}

export class BarcodeScannerService {
  private static instance: BarcodeScannerService;
  private isScanning: boolean = false;
  private scannerConfig: ScannerConfig;
  private lastScanTime: number = 0;
  private scanCallbacks: ((result: BarcodeResult) => void)[] = [];
  private errorCallbacks: ((error: Error) => void)[] = [];
  private usbScannerListener: ((event: KeyboardEvent) => void) | null = null;
  private usbBuffer: string = '';
  private usbTimeout: NodeJS.Timeout | null = null;

  private constructor() {
    this.scannerConfig = {
      enableCamera: true,
      enableUSB: true,
      autoStart: false,
      beepOnScan: true,
      vibrationOnScan: true,
      scanDelay: 1000,
      supportedFormats: [
        'CODE_128',
        'CODE_39',
        'EAN_13',
        'EAN_8',
        'UPC_A',
        'UPC_E',
        'QR_CODE',
        'DATA_MATRIX',
      ],
    };
  }

  static getInstance(): BarcodeScannerService {
    if (!BarcodeScannerService.instance) {
      BarcodeScannerService.instance = new BarcodeScannerService();
    }
    return BarcodeScannerService.instance;
  }

  // Start scanning
  async startScanning(): Promise<void> {
    if (this.isScanning) {
      return;
    }

    try {
      this.isScanning = true;

      // Start USB scanner listener if enabled
      if (this.scannerConfig.enableUSB) {
        this.startUSBScanner();
      }

      console.log('Barcode scanner started');
    } catch (error) {
      this.isScanning = false;
      this.notifyError(error as Error);
      throw error;
    }
  }

  // Stop scanning
  stopScanning(): void {
    if (!this.isScanning) {
      return;
    }

    this.isScanning = false;

    // Stop USB scanner
    this.stopUSBScanner();

    console.log('Barcode scanner stopped');
  }

  // Start USB scanner listener
  private startUSBScanner(): void {
    this.usbScannerListener = (event: KeyboardEvent) => {
      // Prevent default behavior for scanner input
      if (this.isLikelyFromScanner(event)) {
        event.preventDefault();
        this.handleUSBScannerInput(event);
      }
    };

    document.addEventListener('keydown', this.usbScannerListener, true);
    console.log('USB scanner listener started');
  }

  // Stop USB scanner listener
  private stopUSBScanner(): void {
    if (this.usbScannerListener) {
      document.removeEventListener('keydown', this.usbScannerListener, true);
      this.usbScannerListener = null;
    }

    if (this.usbTimeout) {
      clearTimeout(this.usbTimeout);
      this.usbTimeout = null;
    }

    this.usbBuffer = '';
    console.log('USB scanner listener stopped');
  }

  // Handle USB scanner input
  private handleUSBScannerInput(event: KeyboardEvent): void {
    const now = Date.now();

    // Reset buffer if too much time has passed
    if (now - this.lastScanTime > 100) {
      this.usbBuffer = '';
    }

    this.lastScanTime = now;

    // Handle Enter key (end of barcode)
    if (event.key === 'Enter') {
      if (this.usbBuffer.length > 0) {
        this.processScannedCode(this.usbBuffer, 'USB_SCANNER');
        this.usbBuffer = '';
      }
      return;
    }

    // Handle regular characters
    if (event.key.length === 1) {
      this.usbBuffer += event.key;

      // Clear timeout and set new one
      if (this.usbTimeout) {
        clearTimeout(this.usbTimeout);
      }

      this.usbTimeout = setTimeout(() => {
        if (this.usbBuffer.length > 0) {
          this.processScannedCode(this.usbBuffer, 'USB_SCANNER');
          this.usbBuffer = '';
        }
      }, 50); // 50ms timeout for USB scanner
    }
  }

  // Check if keyboard event is likely from a scanner
  private isLikelyFromScanner(event: KeyboardEvent): boolean {
    // USB scanners typically:
    // 1. Send characters very quickly
    // 2. Don't have modifier keys pressed
    // 3. Send printable characters or Enter
    
    const now = Date.now();
    const timeSinceLastKey = now - this.lastScanTime;
    
    return (
      !event.ctrlKey &&
      !event.altKey &&
      !event.metaKey &&
      (event.key.length === 1 || event.key === 'Enter') &&
      (timeSinceLastKey < 100 || this.usbBuffer.length === 0)
    );
  }

  // Process scanned barcode
  private processScannedCode(code: string, source: string): void {
    const now = Date.now();

    // Check scan delay
    if (now - this.lastScanTime < this.scannerConfig.scanDelay) {
      return;
    }

    // Validate barcode
    if (!this.isValidBarcode(code)) {
      return;
    }

    const result: BarcodeResult = {
      code: code.trim(),
      format: this.detectBarcodeFormat(code),
      timestamp: new Date(),
      confidence: source === 'USB_SCANNER' ? 1.0 : undefined,
    };

    // Provide feedback
    this.provideFeedback();

    // Notify callbacks
    this.notifySuccess(result);

    this.lastScanTime = now;
  }

  // Validate barcode
  private isValidBarcode(code: string): boolean {
    const trimmedCode = code.trim();
    
    // Basic validation
    if (trimmedCode.length < 4 || trimmedCode.length > 50) {
      return false;
    }

    // Check for valid characters (alphanumeric and some special chars)
    const validPattern = /^[A-Za-z0-9\-_\.]+$/;
    return validPattern.test(trimmedCode);
  }

  // Detect barcode format
  private detectBarcodeFormat(code: string): string {
    const trimmedCode = code.trim();
    
    // EAN-13
    if (/^\d{13}$/.test(trimmedCode)) {
      return 'EAN_13';
    }
    
    // EAN-8
    if (/^\d{8}$/.test(trimmedCode)) {
      return 'EAN_8';
    }
    
    // UPC-A
    if (/^\d{12}$/.test(trimmedCode)) {
      return 'UPC_A';
    }
    
    // UPC-E
    if (/^\d{6,8}$/.test(trimmedCode)) {
      return 'UPC_E';
    }
    
    // Code 128 (mixed alphanumeric)
    if (/^[A-Za-z0-9\-_\.]+$/.test(trimmedCode) && trimmedCode.length >= 6) {
      return 'CODE_128';
    }
    
    // Default
    return 'UNKNOWN';
  }

  // Provide feedback (beep, vibration)
  private provideFeedback(): void {
    if (this.scannerConfig.beepOnScan) {
      this.playBeep();
    }

    if (this.scannerConfig.vibrationOnScan && 'vibrate' in navigator) {
      navigator.vibrate(100);
    }
  }

  // Play beep sound
  private playBeep(): void {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = 800;
      oscillator.type = 'square';

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + 0.1);
    } catch (error) {
      console.warn('Could not play beep sound:', error);
    }
  }

  // Camera scanning methods
  async startCameraScanning(videoElement: HTMLVideoElement): Promise<void> {
    if (!this.scannerConfig.enableCamera) {
      throw new Error('Camera scanning is disabled');
    }

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      });

      videoElement.srcObject = stream;
      await videoElement.play();

      // Start processing video frames
      this.processCameraFrames(videoElement);
    } catch (error) {
      this.notifyError(error as Error);
      throw error;
    }
  }

  // Process camera frames for barcode detection
  private processCameraFrames(videoElement: HTMLVideoElement): void {
    // This would integrate with a barcode detection library like QuaggaJS
    // For now, we'll simulate the process
    console.log('Processing camera frames for barcode detection');
    
    // TODO: Implement actual barcode detection from video frames
    // This would involve:
    // 1. Capturing frames from video
    // 2. Processing with barcode detection library
    // 3. Calling processScannedCode when barcode is found
  }

  // Stop camera scanning
  stopCameraScanning(videoElement: HTMLVideoElement): void {
    const stream = videoElement.srcObject as MediaStream;
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      videoElement.srcObject = null;
    }
  }

  // Event handling
  onScan(callback: (result: BarcodeResult) => void): void {
    this.scanCallbacks.push(callback);
  }

  onError(callback: (error: Error) => void): void {
    this.errorCallbacks.push(callback);
  }

  private notifySuccess(result: BarcodeResult): void {
    this.scanCallbacks.forEach(callback => {
      try {
        callback(result);
      } catch (error) {
        console.error('Error in scan callback:', error);
      }
    });
  }

  private notifyError(error: Error): void {
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error);
      } catch (err) {
        console.error('Error in error callback:', err);
      }
    });
  }

  // Configuration
  updateConfig(config: Partial<ScannerConfig>): void {
    this.scannerConfig = { ...this.scannerConfig, ...config };
  }

  getConfig(): ScannerConfig {
    return { ...this.scannerConfig };
  }

  // Status
  isCurrentlyScanning(): boolean {
    return this.isScanning;
  }

  // Manual barcode input
  processManualInput(code: string): void {
    this.processScannedCode(code, 'MANUAL');
  }
}

// Export singleton instance
export const barcodeScannerService = BarcodeScannerService.getInstance();
