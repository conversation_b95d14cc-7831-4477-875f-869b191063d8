{"version": 3, "sources": ["browser-external:node:http", "browser-external:node:https", "browser-external:node:zlib", "browser-external:node:stream", "browser-external:node:buffer", "browser-external:node:util", "browser-external:node:url", "browser-external:node:net", "../../node-fetch/src/index.js", "../../data-uri-to-buffer/src/index.ts", "../../node-fetch/src/body.js", "../../node-fetch/src/errors/base.js", "../../node-fetch/src/errors/fetch-error.js", "../../node-fetch/src/utils/is.js", "../../node-fetch/src/headers.js", "../../node-fetch/src/utils/is-redirect.js", "../../node-fetch/src/response.js", "../../node-fetch/src/request.js", "../../node-fetch/src/utils/get-search.js", "../../node-fetch/src/utils/referrer.js", "../../node-fetch/src/errors/abort-error.js"], "sourcesContent": ["module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:http\" has been externalized for browser compatibility. Cannot access \"node:http.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:https\" has been externalized for browser compatibility. Cannot access \"node:https.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:zlib\" has been externalized for browser compatibility. Cannot access \"node:zlib.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:stream\" has been externalized for browser compatibility. Cannot access \"node:stream.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:buffer\" has been externalized for browser compatibility. Cannot access \"node:buffer.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:util\" has been externalized for browser compatibility. Cannot access \"node:util.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:url\" has been externalized for browser compatibility. Cannot access \"node:url.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "module.exports = Object.create(new Proxy({}, {\n  get(_, key) {\n    if (\n      key !== '__esModule' &&\n      key !== '__proto__' &&\n      key !== 'constructor' &&\n      key !== 'splice'\n    ) {\n      console.warn(`Module \"node:net\" has been externalized for browser compatibility. Cannot access \"node:net.${key}\" in client code. See https://vite.dev/guide/troubleshooting.html#module-externalized-for-browser-compatibility for more details.`)\n    }\n  }\n}))", "/**\n * Index.js\n *\n * a request API compatible with window.fetch\n *\n * All spec algorithm step numbers are based on https://fetch.spec.whatwg.org/commit-snapshots/ae716822cb3a61843226cd090eefc6589446c1d2/.\n */\n\nimport http from 'node:http';\nimport https from 'node:https';\nimport zlib from 'node:zlib';\nimport Stream, {PassThrough, pipeline as pump} from 'node:stream';\nimport {Buffer} from 'node:buffer';\n\nimport dataUriToBuffer from 'data-uri-to-buffer';\n\nimport {writeToStream, clone} from './body.js';\nimport Response from './response.js';\nimport Headers, {fromRawHeaders} from './headers.js';\nimport Request, {getNodeRequestOptions} from './request.js';\nimport {FetchError} from './errors/fetch-error.js';\nimport {AbortError} from './errors/abort-error.js';\nimport {isRedirect} from './utils/is-redirect.js';\nimport {FormData} from 'formdata-polyfill/esm.min.js';\nimport {isDomainOrSubdomain, isSameProtocol} from './utils/is.js';\nimport {parseReferrerPolicyFromHeader} from './utils/referrer.js';\nimport {\n\tBlob,\n\tFile,\n\tfileFromSync,\n\tfileFrom,\n\tblobFromSync,\n\tblobFrom\n} from 'fetch-blob/from.js';\n\nexport {FormData, Headers, Request, Response, FetchError, AbortError, isRedirect};\nexport {Blob, File, fileFromSync, fileFrom, blobFromSync, blobFrom};\n\nconst supportedSchemas = new Set(['data:', 'http:', 'https:']);\n\n/**\n * Fetch function\n *\n * @param   {string | URL | import('./request').default} url - Absolute url or Request instance\n * @param   {*} [options_] - Fetch options\n * @return  {Promise<import('./response').default>}\n */\nexport default async function fetch(url, options_) {\n\treturn new Promise((resolve, reject) => {\n\t\t// Build request object\n\t\tconst request = new Request(url, options_);\n\t\tconst {parsedURL, options} = getNodeRequestOptions(request);\n\t\tif (!supportedSchemas.has(parsedURL.protocol)) {\n\t\t\tthrow new TypeError(`node-fetch cannot load ${url}. URL scheme \"${parsedURL.protocol.replace(/:$/, '')}\" is not supported.`);\n\t\t}\n\n\t\tif (parsedURL.protocol === 'data:') {\n\t\t\tconst data = dataUriToBuffer(request.url);\n\t\t\tconst response = new Response(data, {headers: {'Content-Type': data.typeFull}});\n\t\t\tresolve(response);\n\t\t\treturn;\n\t\t}\n\n\t\t// Wrap http.request into fetch\n\t\tconst send = (parsedURL.protocol === 'https:' ? https : http).request;\n\t\tconst {signal} = request;\n\t\tlet response = null;\n\n\t\tconst abort = () => {\n\t\t\tconst error = new AbortError('The operation was aborted.');\n\t\t\treject(error);\n\t\t\tif (request.body && request.body instanceof Stream.Readable) {\n\t\t\t\trequest.body.destroy(error);\n\t\t\t}\n\n\t\t\tif (!response || !response.body) {\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tresponse.body.emit('error', error);\n\t\t};\n\n\t\tif (signal && signal.aborted) {\n\t\t\tabort();\n\t\t\treturn;\n\t\t}\n\n\t\tconst abortAndFinalize = () => {\n\t\t\tabort();\n\t\t\tfinalize();\n\t\t};\n\n\t\t// Send request\n\t\tconst request_ = send(parsedURL.toString(), options);\n\n\t\tif (signal) {\n\t\t\tsignal.addEventListener('abort', abortAndFinalize);\n\t\t}\n\n\t\tconst finalize = () => {\n\t\t\trequest_.abort();\n\t\t\tif (signal) {\n\t\t\t\tsignal.removeEventListener('abort', abortAndFinalize);\n\t\t\t}\n\t\t};\n\n\t\trequest_.on('error', error => {\n\t\t\treject(new FetchError(`request to ${request.url} failed, reason: ${error.message}`, 'system', error));\n\t\t\tfinalize();\n\t\t});\n\n\t\tfixResponseChunkedTransferBadEnding(request_, error => {\n\t\t\tif (response && response.body) {\n\t\t\t\tresponse.body.destroy(error);\n\t\t\t}\n\t\t});\n\n\t\t/* c8 ignore next 18 */\n\t\tif (process.version < 'v14') {\n\t\t\t// Before Node.js 14, pipeline() does not fully support async iterators and does not always\n\t\t\t// properly handle when the socket close/end events are out of order.\n\t\t\trequest_.on('socket', s => {\n\t\t\t\tlet endedWithEventsCount;\n\t\t\t\ts.prependListener('end', () => {\n\t\t\t\t\tendedWithEventsCount = s._eventsCount;\n\t\t\t\t});\n\t\t\t\ts.prependListener('close', hadError => {\n\t\t\t\t\t// if end happened before close but the socket didn't emit an error, do it now\n\t\t\t\t\tif (response && endedWithEventsCount < s._eventsCount && !hadError) {\n\t\t\t\t\t\tconst error = new Error('Premature close');\n\t\t\t\t\t\terror.code = 'ERR_STREAM_PREMATURE_CLOSE';\n\t\t\t\t\t\tresponse.body.emit('error', error);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t});\n\t\t}\n\n\t\trequest_.on('response', response_ => {\n\t\t\trequest_.setTimeout(0);\n\t\t\tconst headers = fromRawHeaders(response_.rawHeaders);\n\n\t\t\t// HTTP fetch step 5\n\t\t\tif (isRedirect(response_.statusCode)) {\n\t\t\t\t// HTTP fetch step 5.2\n\t\t\t\tconst location = headers.get('Location');\n\n\t\t\t\t// HTTP fetch step 5.3\n\t\t\t\tlet locationURL = null;\n\t\t\t\ttry {\n\t\t\t\t\tlocationURL = location === null ? null : new URL(location, request.url);\n\t\t\t\t} catch {\n\t\t\t\t\t// error here can only be invalid URL in Location: header\n\t\t\t\t\t// do not throw when options.redirect == manual\n\t\t\t\t\t// let the user extract the errorneous redirect URL\n\t\t\t\t\tif (request.redirect !== 'manual') {\n\t\t\t\t\t\treject(new FetchError(`uri requested responds with an invalid redirect URL: ${location}`, 'invalid-redirect'));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// HTTP fetch step 5.5\n\t\t\t\tswitch (request.redirect) {\n\t\t\t\t\tcase 'error':\n\t\t\t\t\t\treject(new FetchError(`uri requested responds with a redirect, redirect mode is set to error: ${request.url}`, 'no-redirect'));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t\tcase 'manual':\n\t\t\t\t\t\t// Nothing to do\n\t\t\t\t\t\tbreak;\n\t\t\t\t\tcase 'follow': {\n\t\t\t\t\t\t// HTTP-redirect fetch step 2\n\t\t\t\t\t\tif (locationURL === null) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 5\n\t\t\t\t\t\tif (request.counter >= request.follow) {\n\t\t\t\t\t\t\treject(new FetchError(`maximum redirect reached at: ${request.url}`, 'max-redirect'));\n\t\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 6 (counter increment)\n\t\t\t\t\t\t// Create a new Request object.\n\t\t\t\t\t\tconst requestOptions = {\n\t\t\t\t\t\t\theaders: new Headers(request.headers),\n\t\t\t\t\t\t\tfollow: request.follow,\n\t\t\t\t\t\t\tcounter: request.counter + 1,\n\t\t\t\t\t\t\tagent: request.agent,\n\t\t\t\t\t\t\tcompress: request.compress,\n\t\t\t\t\t\t\tmethod: request.method,\n\t\t\t\t\t\t\tbody: clone(request),\n\t\t\t\t\t\t\tsignal: request.signal,\n\t\t\t\t\t\t\tsize: request.size,\n\t\t\t\t\t\t\treferrer: request.referrer,\n\t\t\t\t\t\t\treferrerPolicy: request.referrerPolicy\n\t\t\t\t\t\t};\n\n\t\t\t\t\t\t// when forwarding sensitive headers like \"Authorization\",\n\t\t\t\t\t\t// \"WWW-Authenticate\", and \"Cookie\" to untrusted targets,\n\t\t\t\t\t\t// headers will be ignored when following a redirect to a domain\n\t\t\t\t\t\t// that is not a subdomain match or exact match of the initial domain.\n\t\t\t\t\t\t// For example, a redirect from \"foo.com\" to either \"foo.com\" or \"sub.foo.com\"\n\t\t\t\t\t\t// will forward the sensitive headers, but a redirect to \"bar.com\" will not.\n\t\t\t\t\t\t// headers will also be ignored when following a redirect to a domain using\n\t\t\t\t\t\t// a different protocol. For example, a redirect from \"https://foo.com\" to \"http://foo.com\"\n\t\t\t\t\t\t// will not forward the sensitive headers\n\t\t\t\t\t\tif (!isDomainOrSubdomain(request.url, locationURL) || !isSameProtocol(request.url, locationURL)) {\n\t\t\t\t\t\t\tfor (const name of ['authorization', 'www-authenticate', 'cookie', 'cookie2']) {\n\t\t\t\t\t\t\t\trequestOptions.headers.delete(name);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 9\n\t\t\t\t\t\tif (response_.statusCode !== 303 && request.body && options_.body instanceof Stream.Readable) {\n\t\t\t\t\t\t\treject(new FetchError('Cannot follow redirect with body being a readable stream', 'unsupported-redirect'));\n\t\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 11\n\t\t\t\t\t\tif (response_.statusCode === 303 || ((response_.statusCode === 301 || response_.statusCode === 302) && request.method === 'POST')) {\n\t\t\t\t\t\t\trequestOptions.method = 'GET';\n\t\t\t\t\t\t\trequestOptions.body = undefined;\n\t\t\t\t\t\t\trequestOptions.headers.delete('content-length');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 14\n\t\t\t\t\t\tconst responseReferrerPolicy = parseReferrerPolicyFromHeader(headers);\n\t\t\t\t\t\tif (responseReferrerPolicy) {\n\t\t\t\t\t\t\trequestOptions.referrerPolicy = responseReferrerPolicy;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// HTTP-redirect fetch step 15\n\t\t\t\t\t\tresolve(fetch(new Request(locationURL, requestOptions)));\n\t\t\t\t\t\tfinalize();\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn reject(new TypeError(`Redirect option '${request.redirect}' is not a valid value of RequestRedirect`));\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Prepare response\n\t\t\tif (signal) {\n\t\t\t\tresponse_.once('end', () => {\n\t\t\t\t\tsignal.removeEventListener('abort', abortAndFinalize);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tlet body = pump(response_, new PassThrough(), error => {\n\t\t\t\tif (error) {\n\t\t\t\t\treject(error);\n\t\t\t\t}\n\t\t\t});\n\t\t\t// see https://github.com/nodejs/node/pull/29376\n\t\t\t/* c8 ignore next 3 */\n\t\t\tif (process.version < 'v12.10') {\n\t\t\t\tresponse_.on('aborted', abortAndFinalize);\n\t\t\t}\n\n\t\t\tconst responseOptions = {\n\t\t\t\turl: request.url,\n\t\t\t\tstatus: response_.statusCode,\n\t\t\t\tstatusText: response_.statusMessage,\n\t\t\t\theaders,\n\t\t\t\tsize: request.size,\n\t\t\t\tcounter: request.counter,\n\t\t\t\thighWaterMark: request.highWaterMark\n\t\t\t};\n\n\t\t\t// HTTP-network fetch step 12.1.1.3\n\t\t\tconst codings = headers.get('Content-Encoding');\n\n\t\t\t// HTTP-network fetch step 12.1.1.4: handle content codings\n\n\t\t\t// in following scenarios we ignore compression support\n\t\t\t// 1. compression support is disabled\n\t\t\t// 2. HEAD request\n\t\t\t// 3. no Content-Encoding header\n\t\t\t// 4. no content response (204)\n\t\t\t// 5. content not modified response (304)\n\t\t\tif (!request.compress || request.method === 'HEAD' || codings === null || response_.statusCode === 204 || response_.statusCode === 304) {\n\t\t\t\tresponse = new Response(body, responseOptions);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// For Node v6+\n\t\t\t// Be less strict when decoding compressed responses, since sometimes\n\t\t\t// servers send slightly invalid responses that are still accepted\n\t\t\t// by common browsers.\n\t\t\t// Always using Z_SYNC_FLUSH is what cURL does.\n\t\t\tconst zlibOptions = {\n\t\t\t\tflush: zlib.Z_SYNC_FLUSH,\n\t\t\t\tfinishFlush: zlib.Z_SYNC_FLUSH\n\t\t\t};\n\n\t\t\t// For gzip\n\t\t\tif (codings === 'gzip' || codings === 'x-gzip') {\n\t\t\t\tbody = pump(body, zlib.createGunzip(zlibOptions), error => {\n\t\t\t\t\tif (error) {\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tresponse = new Response(body, responseOptions);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// For deflate\n\t\t\tif (codings === 'deflate' || codings === 'x-deflate') {\n\t\t\t\t// Handle the infamous raw deflate response from old servers\n\t\t\t\t// a hack for old IIS and Apache servers\n\t\t\t\tconst raw = pump(response_, new PassThrough(), error => {\n\t\t\t\t\tif (error) {\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\traw.once('data', chunk => {\n\t\t\t\t\t// See http://stackoverflow.com/questions/37519828\n\t\t\t\t\tif ((chunk[0] & 0x0F) === 0x08) {\n\t\t\t\t\t\tbody = pump(body, zlib.createInflate(), error => {\n\t\t\t\t\t\t\tif (error) {\n\t\t\t\t\t\t\t\treject(error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t} else {\n\t\t\t\t\t\tbody = pump(body, zlib.createInflateRaw(), error => {\n\t\t\t\t\t\t\tif (error) {\n\t\t\t\t\t\t\t\treject(error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\n\t\t\t\t\tresponse = new Response(body, responseOptions);\n\t\t\t\t\tresolve(response);\n\t\t\t\t});\n\t\t\t\traw.once('end', () => {\n\t\t\t\t\t// Some old IIS servers return zero-length OK deflate responses, so\n\t\t\t\t\t// 'data' is never emitted. See https://github.com/node-fetch/node-fetch/pull/903\n\t\t\t\t\tif (!response) {\n\t\t\t\t\t\tresponse = new Response(body, responseOptions);\n\t\t\t\t\t\tresolve(response);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// For br\n\t\t\tif (codings === 'br') {\n\t\t\t\tbody = pump(body, zlib.createBrotliDecompress(), error => {\n\t\t\t\t\tif (error) {\n\t\t\t\t\t\treject(error);\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t\tresponse = new Response(body, responseOptions);\n\t\t\t\tresolve(response);\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Otherwise, use response as-is\n\t\t\tresponse = new Response(body, responseOptions);\n\t\t\tresolve(response);\n\t\t});\n\n\t\t// eslint-disable-next-line promise/prefer-await-to-then\n\t\twriteToStream(request_, request).catch(reject);\n\t});\n}\n\nfunction fixResponseChunkedTransferBadEnding(request, errorCallback) {\n\tconst LAST_CHUNK = Buffer.from('0\\r\\n\\r\\n');\n\n\tlet isChunkedTransfer = false;\n\tlet properLastChunkReceived = false;\n\tlet previousChunk;\n\n\trequest.on('response', response => {\n\t\tconst {headers} = response;\n\t\tisChunkedTransfer = headers['transfer-encoding'] === 'chunked' && !headers['content-length'];\n\t});\n\n\trequest.on('socket', socket => {\n\t\tconst onSocketClose = () => {\n\t\t\tif (isChunkedTransfer && !properLastChunkReceived) {\n\t\t\t\tconst error = new Error('Premature close');\n\t\t\t\terror.code = 'ERR_STREAM_PREMATURE_CLOSE';\n\t\t\t\terrorCallback(error);\n\t\t\t}\n\t\t};\n\n\t\tconst onData = buf => {\n\t\t\tproperLastChunkReceived = Buffer.compare(buf.slice(-5), LAST_CHUNK) === 0;\n\n\t\t\t// Sometimes final 0-length chunk and end of message code are in separate packets\n\t\t\tif (!properLastChunkReceived && previousChunk) {\n\t\t\t\tproperLastChunkReceived = (\n\t\t\t\t\tBuffer.compare(previousChunk.slice(-3), LAST_CHUNK.slice(0, 3)) === 0 &&\n\t\t\t\t\tBuffer.compare(buf.slice(-2), LAST_CHUNK.slice(3)) === 0\n\t\t\t\t);\n\t\t\t}\n\n\t\t\tpreviousChunk = buf;\n\t\t};\n\n\t\tsocket.prependListener('close', onSocketClose);\n\t\tsocket.on('data', onData);\n\n\t\trequest.on('close', () => {\n\t\t\tsocket.removeListener('close', onSocketClose);\n\t\t\tsocket.removeListener('data', onData);\n\t\t});\n\t});\n}\n", "export interface MimeBuffer extends Buffer {\n\ttype: string;\n\ttypeFull: string;\n\tcharset: string;\n}\n\n/**\n * Returns a `<PERSON><PERSON><PERSON>` instance from the given data URI `uri`.\n *\n * @param {String} uri Data URI to turn into a Buffer instance\n * @returns {Buffer} Buffer instance from Data URI\n * @api public\n */\nexport function dataUriToBuffer(uri: string): MimeBuffer {\n\tif (!/^data:/i.test(uri)) {\n\t\tthrow new TypeError(\n\t\t\t'`uri` does not appear to be a Data URI (must begin with \"data:\")'\n\t\t);\n\t}\n\n\t// strip newlines\n\turi = uri.replace(/\\r?\\n/g, '');\n\n\t// split the URI up into the \"metadata\" and the \"data\" portions\n\tconst firstComma = uri.indexOf(',');\n\tif (firstComma === -1 || firstComma <= 4) {\n\t\tthrow new TypeError('malformed data: URI');\n\t}\n\n\t// remove the \"data:\" scheme and parse the metadata\n\tconst meta = uri.substring(5, firstComma).split(';');\n\n\tlet charset = '';\n\tlet base64 = false;\n\tconst type = meta[0] || 'text/plain';\n\tlet typeFull = type;\n\tfor (let i = 1; i < meta.length; i++) {\n\t\tif (meta[i] === 'base64') {\n\t\t\tbase64 = true;\n\t\t} else if(meta[i]) {\n\t\t\ttypeFull += `;${  meta[i]}`;\n\t\t\tif (meta[i].indexOf('charset=') === 0) {\n\t\t\t\tcharset = meta[i].substring(8);\n\t\t\t}\n\t\t}\n\t}\n\t// defaults to US-ASCII only if type is not provided\n\tif (!meta[0] && !charset.length) {\n\t\ttypeFull += ';charset=US-ASCII';\n\t\tcharset = 'US-ASCII';\n\t}\n\n\t// get the encoded data portion and decode URI-encoded chars\n\tconst encoding = base64 ? 'base64' : 'ascii';\n\tconst data = unescape(uri.substring(firstComma + 1));\n\tconst buffer = Buffer.from(data, encoding) as MimeBuffer;\n\n\t// set `.type` and `.typeFull` properties to MIME type\n\tbuffer.type = type;\n\tbuffer.typeFull = typeFull;\n\n\t// set the `.charset` property\n\tbuffer.charset = charset;\n\n\treturn buffer;\n}\n\nexport default dataUriToBuffer;\n", "\n/**\n * Body.js\n *\n * Body interface provides common methods for Request and Response\n */\n\nimport Stream, {PassThrough} from 'node:stream';\nimport {types, deprecate, promisify} from 'node:util';\nimport {Buffer} from 'node:buffer';\n\nimport Blob from 'fetch-blob';\nimport {FormData, formDataToBlob} from 'formdata-polyfill/esm.min.js';\n\nimport {FetchError} from './errors/fetch-error.js';\nimport {FetchBaseError} from './errors/base.js';\nimport {isBlob, isURLSearchParameters} from './utils/is.js';\n\nconst pipeline = promisify(Stream.pipeline);\nconst INTERNALS = Symbol('Body internals');\n\n/**\n * Body mixin\n *\n * Ref: https://fetch.spec.whatwg.org/#body\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */\nexport default class Body {\n\tconstructor(body, {\n\t\tsize = 0\n\t} = {}) {\n\t\tlet boundary = null;\n\n\t\tif (body === null) {\n\t\t\t// Body is undefined or null\n\t\t\tbody = null;\n\t\t} else if (isURLSearchParameters(body)) {\n\t\t\t// Body is a URLSearchParams\n\t\t\tbody = Buffer.from(body.toString());\n\t\t} else if (isBlob(body)) {\n\t\t\t// Body is blob\n\t\t} else if (Buffer.isBuffer(body)) {\n\t\t\t// Body is Buffer\n\t\t} else if (types.isAnyArrayBuffer(body)) {\n\t\t\t// Body is ArrayBuffer\n\t\t\tbody = Buffer.from(body);\n\t\t} else if (ArrayBuffer.isView(body)) {\n\t\t\t// Body is ArrayBufferView\n\t\t\tbody = Buffer.from(body.buffer, body.byteOffset, body.byteLength);\n\t\t} else if (body instanceof Stream) {\n\t\t\t// Body is stream\n\t\t} else if (body instanceof FormData) {\n\t\t\t// Body is FormData\n\t\t\tbody = formDataToBlob(body);\n\t\t\tboundary = body.type.split('=')[1];\n\t\t} else {\n\t\t\t// None of the above\n\t\t\t// coerce to string then buffer\n\t\t\tbody = Buffer.from(String(body));\n\t\t}\n\n\t\tlet stream = body;\n\n\t\tif (Buffer.isBuffer(body)) {\n\t\t\tstream = Stream.Readable.from(body);\n\t\t} else if (isBlob(body)) {\n\t\t\tstream = Stream.Readable.from(body.stream());\n\t\t}\n\n\t\tthis[INTERNALS] = {\n\t\t\tbody,\n\t\t\tstream,\n\t\t\tboundary,\n\t\t\tdisturbed: false,\n\t\t\terror: null\n\t\t};\n\t\tthis.size = size;\n\n\t\tif (body instanceof Stream) {\n\t\t\tbody.on('error', error_ => {\n\t\t\t\tconst error = error_ instanceof FetchBaseError ?\n\t\t\t\t\terror_ :\n\t\t\t\t\tnew FetchError(`Invalid response body while trying to fetch ${this.url}: ${error_.message}`, 'system', error_);\n\t\t\t\tthis[INTERNALS].error = error;\n\t\t\t});\n\t\t}\n\t}\n\n\tget body() {\n\t\treturn this[INTERNALS].stream;\n\t}\n\n\tget bodyUsed() {\n\t\treturn this[INTERNALS].disturbed;\n\t}\n\n\t/**\n\t * Decode response as ArrayBuffer\n\t *\n\t * @return  Promise\n\t */\n\tasync arrayBuffer() {\n\t\tconst {buffer, byteOffset, byteLength} = await consumeBody(this);\n\t\treturn buffer.slice(byteOffset, byteOffset + byteLength);\n\t}\n\n\tasync formData() {\n\t\tconst ct = this.headers.get('content-type');\n\n\t\tif (ct.startsWith('application/x-www-form-urlencoded')) {\n\t\t\tconst formData = new FormData();\n\t\t\tconst parameters = new URLSearchParams(await this.text());\n\n\t\t\tfor (const [name, value] of parameters) {\n\t\t\t\tformData.append(name, value);\n\t\t\t}\n\n\t\t\treturn formData;\n\t\t}\n\n\t\tconst {toFormData} = await import('./utils/multipart-parser.js');\n\t\treturn toFormData(this.body, ct);\n\t}\n\n\t/**\n\t * Return raw response as Blob\n\t *\n\t * @return Promise\n\t */\n\tasync blob() {\n\t\tconst ct = (this.headers && this.headers.get('content-type')) || (this[INTERNALS].body && this[INTERNALS].body.type) || '';\n\t\tconst buf = await this.arrayBuffer();\n\n\t\treturn new Blob([buf], {\n\t\t\ttype: ct\n\t\t});\n\t}\n\n\t/**\n\t * Decode response as json\n\t *\n\t * @return  Promise\n\t */\n\tasync json() {\n\t\tconst text = await this.text();\n\t\treturn JSON.parse(text);\n\t}\n\n\t/**\n\t * Decode response as text\n\t *\n\t * @return  Promise\n\t */\n\tasync text() {\n\t\tconst buffer = await consumeBody(this);\n\t\treturn new TextDecoder().decode(buffer);\n\t}\n\n\t/**\n\t * Decode response as buffer (non-spec api)\n\t *\n\t * @return  Promise\n\t */\n\tbuffer() {\n\t\treturn consumeBody(this);\n\t}\n}\n\nBody.prototype.buffer = deprecate(Body.prototype.buffer, 'Please use \\'response.arrayBuffer()\\' instead of \\'response.buffer()\\'', 'node-fetch#buffer');\n\n// In browsers, all properties are enumerable.\nObject.defineProperties(Body.prototype, {\n\tbody: {enumerable: true},\n\tbodyUsed: {enumerable: true},\n\tarrayBuffer: {enumerable: true},\n\tblob: {enumerable: true},\n\tjson: {enumerable: true},\n\ttext: {enumerable: true},\n\tdata: {get: deprecate(() => {},\n\t\t'data doesn\\'t exist, use json(), text(), arrayBuffer(), or body instead',\n\t\t'https://github.com/node-fetch/node-fetch/issues/1000 (response)')}\n});\n\n/**\n * Consume and convert an entire Body to a Buffer.\n *\n * Ref: https://fetch.spec.whatwg.org/#concept-body-consume-body\n *\n * @return Promise\n */\nasync function consumeBody(data) {\n\tif (data[INTERNALS].disturbed) {\n\t\tthrow new TypeError(`body used already for: ${data.url}`);\n\t}\n\n\tdata[INTERNALS].disturbed = true;\n\n\tif (data[INTERNALS].error) {\n\t\tthrow data[INTERNALS].error;\n\t}\n\n\tconst {body} = data;\n\n\t// Body is null\n\tif (body === null) {\n\t\treturn Buffer.alloc(0);\n\t}\n\n\t/* c8 ignore next 3 */\n\tif (!(body instanceof Stream)) {\n\t\treturn Buffer.alloc(0);\n\t}\n\n\t// Body is stream\n\t// get ready to actually consume the body\n\tconst accum = [];\n\tlet accumBytes = 0;\n\n\ttry {\n\t\tfor await (const chunk of body) {\n\t\t\tif (data.size > 0 && accumBytes + chunk.length > data.size) {\n\t\t\t\tconst error = new FetchError(`content size at ${data.url} over limit: ${data.size}`, 'max-size');\n\t\t\t\tbody.destroy(error);\n\t\t\t\tthrow error;\n\t\t\t}\n\n\t\t\taccumBytes += chunk.length;\n\t\t\taccum.push(chunk);\n\t\t}\n\t} catch (error) {\n\t\tconst error_ = error instanceof FetchBaseError ? error : new FetchError(`Invalid response body while trying to fetch ${data.url}: ${error.message}`, 'system', error);\n\t\tthrow error_;\n\t}\n\n\tif (body.readableEnded === true || body._readableState.ended === true) {\n\t\ttry {\n\t\t\tif (accum.every(c => typeof c === 'string')) {\n\t\t\t\treturn Buffer.from(accum.join(''));\n\t\t\t}\n\n\t\t\treturn Buffer.concat(accum, accumBytes);\n\t\t} catch (error) {\n\t\t\tthrow new FetchError(`Could not create Buffer from response body for ${data.url}: ${error.message}`, 'system', error);\n\t\t}\n\t} else {\n\t\tthrow new FetchError(`Premature close of server response while trying to fetch ${data.url}`);\n\t}\n}\n\n/**\n * Clone body given Res/Req instance\n *\n * @param   Mixed   instance       Response or Request instance\n * @param   String  highWaterMark  highWaterMark for both PassThrough body streams\n * @return  Mixed\n */\nexport const clone = (instance, highWaterMark) => {\n\tlet p1;\n\tlet p2;\n\tlet {body} = instance[INTERNALS];\n\n\t// Don't allow cloning a used body\n\tif (instance.bodyUsed) {\n\t\tthrow new Error('cannot clone body after it is used');\n\t}\n\n\t// Check that body is a stream and not form-data object\n\t// note: we can't clone the form-data object without having it as a dependency\n\tif ((body instanceof Stream) && (typeof body.getBoundary !== 'function')) {\n\t\t// Tee instance body\n\t\tp1 = new PassThrough({highWaterMark});\n\t\tp2 = new PassThrough({highWaterMark});\n\t\tbody.pipe(p1);\n\t\tbody.pipe(p2);\n\t\t// Set instance body to teed body and return the other teed body\n\t\tinstance[INTERNALS].stream = p1;\n\t\tbody = p2;\n\t}\n\n\treturn body;\n};\n\nconst getNonSpecFormDataBoundary = deprecate(\n\tbody => body.getBoundary(),\n\t'form-data doesn\\'t follow the spec and requires special treatment. Use alternative package',\n\t'https://github.com/node-fetch/node-fetch/issues/1167'\n);\n\n/**\n * Performs the operation \"extract a `Content-Type` value from |object|\" as\n * specified in the specification:\n * https://fetch.spec.whatwg.org/#concept-bodyinit-extract\n *\n * This function assumes that instance.body is present.\n *\n * @param {any} body Any options.body input\n * @returns {string | null}\n */\nexport const extractContentType = (body, request) => {\n\t// Body is null or undefined\n\tif (body === null) {\n\t\treturn null;\n\t}\n\n\t// Body is string\n\tif (typeof body === 'string') {\n\t\treturn 'text/plain;charset=UTF-8';\n\t}\n\n\t// Body is a URLSearchParams\n\tif (isURLSearchParameters(body)) {\n\t\treturn 'application/x-www-form-urlencoded;charset=UTF-8';\n\t}\n\n\t// Body is blob\n\tif (isBlob(body)) {\n\t\treturn body.type || null;\n\t}\n\n\t// Body is a Buffer (Buffer, ArrayBuffer or ArrayBufferView)\n\tif (Buffer.isBuffer(body) || types.isAnyArrayBuffer(body) || ArrayBuffer.isView(body)) {\n\t\treturn null;\n\t}\n\n\tif (body instanceof FormData) {\n\t\treturn `multipart/form-data; boundary=${request[INTERNALS].boundary}`;\n\t}\n\n\t// Detect form data input from form-data module\n\tif (body && typeof body.getBoundary === 'function') {\n\t\treturn `multipart/form-data;boundary=${getNonSpecFormDataBoundary(body)}`;\n\t}\n\n\t// Body is stream - can't really do much about this\n\tif (body instanceof Stream) {\n\t\treturn null;\n\t}\n\n\t// Body constructor defaults other things to string\n\treturn 'text/plain;charset=UTF-8';\n};\n\n/**\n * The Fetch Standard treats this as if \"total bytes\" is a property on the body.\n * For us, we have to explicitly get it with a function.\n *\n * ref: https://fetch.spec.whatwg.org/#concept-body-total-bytes\n *\n * @param {any} obj.body Body object from the Body instance.\n * @returns {number | null}\n */\nexport const getTotalBytes = request => {\n\tconst {body} = request[INTERNALS];\n\n\t// Body is null or undefined\n\tif (body === null) {\n\t\treturn 0;\n\t}\n\n\t// Body is Blob\n\tif (isBlob(body)) {\n\t\treturn body.size;\n\t}\n\n\t// Body is Buffer\n\tif (Buffer.isBuffer(body)) {\n\t\treturn body.length;\n\t}\n\n\t// Detect form data input from form-data module\n\tif (body && typeof body.getLengthSync === 'function') {\n\t\treturn body.hasKnownLength && body.hasKnownLength() ? body.getLengthSync() : null;\n\t}\n\n\t// Body is stream\n\treturn null;\n};\n\n/**\n * Write a Body to a Node.js WritableStream (e.g. http.Request) object.\n *\n * @param {Stream.Writable} dest The stream to write to.\n * @param obj.body Body object from the Body instance.\n * @returns {Promise<void>}\n */\nexport const writeToStream = async (dest, {body}) => {\n\tif (body === null) {\n\t\t// Body is null\n\t\tdest.end();\n\t} else {\n\t\t// Body is stream\n\t\tawait pipeline(body, dest);\n\t}\n};\n", "export class FetchBaseError extends Error {\n\tconstructor(message, type) {\n\t\tsuper(message);\n\t\t// Hide custom error implementation details from end-users\n\t\tError.captureStackTrace(this, this.constructor);\n\n\t\tthis.type = type;\n\t}\n\n\tget name() {\n\t\treturn this.constructor.name;\n\t}\n\n\tget [Symbol.toStringTag]() {\n\t\treturn this.constructor.name;\n\t}\n}\n", "\nimport {FetchBaseError} from './base.js';\n\n/**\n * @typedef {{ address?: string, code: string, dest?: string, errno: number, info?: object, message: string, path?: string, port?: number, syscall: string}} SystemError\n*/\n\n/**\n * FetchError interface for operational errors\n */\nexport class FetchError extends FetchBaseError {\n\t/**\n\t * @param  {string} message -      Error message for human\n\t * @param  {string} [type] -        Error type for machine\n\t * @param  {SystemError} [systemError] - For Node.js system error\n\t */\n\tconstructor(message, type, systemError) {\n\t\tsuper(message, type);\n\t\t// When err.type is `system`, err.erroredSysCall contains system error and err.code contains system error code\n\t\tif (systemError) {\n\t\t\t// eslint-disable-next-line no-multi-assign\n\t\t\tthis.code = this.errno = systemError.code;\n\t\t\tthis.erroredSysCall = systemError.syscall;\n\t\t}\n\t}\n}\n", "/**\n * Is.js\n *\n * Object type checks.\n */\n\nconst NAME = Symbol.toStringTag;\n\n/**\n * Check if `obj` is a URLSearchParams object\n * ref: https://github.com/node-fetch/node-fetch/issues/296#issuecomment-307598143\n * @param {*} object - Object to check for\n * @return {boolean}\n */\nexport const isURLSearchParameters = object => {\n\treturn (\n\t\ttypeof object === 'object' &&\n\t\ttypeof object.append === 'function' &&\n\t\ttypeof object.delete === 'function' &&\n\t\ttypeof object.get === 'function' &&\n\t\ttypeof object.getAll === 'function' &&\n\t\ttypeof object.has === 'function' &&\n\t\ttypeof object.set === 'function' &&\n\t\ttypeof object.sort === 'function' &&\n\t\tobject[NAME] === 'URLSearchParams'\n\t);\n};\n\n/**\n * Check if `object` is a W3C `Blob` object (which `File` inherits from)\n * @param {*} object - Object to check for\n * @return {boolean}\n */\nexport const isBlob = object => {\n\treturn (\n\t\tobject &&\n\t\ttypeof object === 'object' &&\n\t\ttypeof object.arrayBuffer === 'function' &&\n\t\ttypeof object.type === 'string' &&\n\t\ttypeof object.stream === 'function' &&\n\t\ttypeof object.constructor === 'function' &&\n\t\t/^(Blob|File)$/.test(object[NAME])\n\t);\n};\n\n/**\n * Check if `obj` is an instance of AbortSignal.\n * @param {*} object - Object to check for\n * @return {boolean}\n */\nexport const isAbortSignal = object => {\n\treturn (\n\t\ttypeof object === 'object' && (\n\t\t\tobject[NAME] === 'AbortSignal' ||\n\t\t\tobject[NAME] === 'EventTarget'\n\t\t)\n\t);\n};\n\n/**\n * isDomainOrSubdomain reports whether sub is a subdomain (or exact match) of\n * the parent domain.\n *\n * Both domains must already be in canonical form.\n * @param {string|URL} original\n * @param {string|URL} destination\n */\nexport const isDomainOrSubdomain = (destination, original) => {\n\tconst orig = new URL(original).hostname;\n\tconst dest = new URL(destination).hostname;\n\n\treturn orig === dest || orig.endsWith(`.${dest}`);\n};\n\n/**\n * isSameProtocol reports whether the two provided URLs use the same protocol.\n *\n * Both domains must already be in canonical form.\n * @param {string|URL} original\n * @param {string|URL} destination\n */\nexport const isSameProtocol = (destination, original) => {\n\tconst orig = new URL(original).protocol;\n\tconst dest = new URL(destination).protocol;\n\n\treturn orig === dest;\n};\n", "/**\n * Headers.js\n *\n * Headers class offers convenient helpers\n */\n\nimport {types} from 'node:util';\nimport http from 'node:http';\n\n/* c8 ignore next 9 */\nconst validateHeaderName = typeof http.validateHeaderName === 'function' ?\n\thttp.validateHeaderName :\n\tname => {\n\t\tif (!/^[\\^`\\-\\w!#$%&'*+.|~]+$/.test(name)) {\n\t\t\tconst error = new TypeError(`Header name must be a valid HTTP token [${name}]`);\n\t\t\tObject.defineProperty(error, 'code', {value: 'ERR_INVALID_HTTP_TOKEN'});\n\t\t\tthrow error;\n\t\t}\n\t};\n\n/* c8 ignore next 9 */\nconst validateHeaderValue = typeof http.validateHeaderValue === 'function' ?\n\thttp.validateHeaderValue :\n\t(name, value) => {\n\t\tif (/[^\\t\\u0020-\\u007E\\u0080-\\u00FF]/.test(value)) {\n\t\t\tconst error = new TypeError(`Invalid character in header content [\"${name}\"]`);\n\t\t\tObject.defineProperty(error, 'code', {value: 'ERR_INVALID_CHAR'});\n\t\t\tthrow error;\n\t\t}\n\t};\n\n/**\n * @typedef {Headers | Record<string, string> | Iterable<readonly [string, string]> | Iterable<Iterable<string>>} HeadersInit\n */\n\n/**\n * This Fetch API interface allows you to perform various actions on HTTP request and response headers.\n * These actions include retrieving, setting, adding to, and removing.\n * A Headers object has an associated header list, which is initially empty and consists of zero or more name and value pairs.\n * You can add to this using methods like append() (see Examples.)\n * In all methods of this interface, header names are matched by case-insensitive byte sequence.\n *\n */\nexport default class Headers extends URLSearchParams {\n\t/**\n\t * Headers class\n\t *\n\t * @constructor\n\t * @param {HeadersInit} [init] - Response headers\n\t */\n\tconstructor(init) {\n\t\t// Validate and normalize init object in [name, value(s)][]\n\t\t/** @type {string[][]} */\n\t\tlet result = [];\n\t\tif (init instanceof Headers) {\n\t\t\tconst raw = init.raw();\n\t\t\tfor (const [name, values] of Object.entries(raw)) {\n\t\t\t\tresult.push(...values.map(value => [name, value]));\n\t\t\t}\n\t\t} else if (init == null) { // eslint-disable-line no-eq-null, eqeqeq\n\t\t\t// No op\n\t\t} else if (typeof init === 'object' && !types.isBoxedPrimitive(init)) {\n\t\t\tconst method = init[Symbol.iterator];\n\t\t\t// eslint-disable-next-line no-eq-null, eqeqeq\n\t\t\tif (method == null) {\n\t\t\t\t// Record<ByteString, ByteString>\n\t\t\t\tresult.push(...Object.entries(init));\n\t\t\t} else {\n\t\t\t\tif (typeof method !== 'function') {\n\t\t\t\t\tthrow new TypeError('Header pairs must be iterable');\n\t\t\t\t}\n\n\t\t\t\t// Sequence<sequence<ByteString>>\n\t\t\t\t// Note: per spec we have to first exhaust the lists then process them\n\t\t\t\tresult = [...init]\n\t\t\t\t\t.map(pair => {\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\ttypeof pair !== 'object' || types.isBoxedPrimitive(pair)\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tthrow new TypeError('Each header pair must be an iterable object');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn [...pair];\n\t\t\t\t\t}).map(pair => {\n\t\t\t\t\t\tif (pair.length !== 2) {\n\t\t\t\t\t\t\tthrow new TypeError('Each header pair must be a name/value tuple');\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\treturn [...pair];\n\t\t\t\t\t});\n\t\t\t}\n\t\t} else {\n\t\t\tthrow new TypeError('Failed to construct \\'Headers\\': The provided value is not of type \\'(sequence<sequence<ByteString>> or record<ByteString, ByteString>)');\n\t\t}\n\n\t\t// Validate and lowercase\n\t\tresult =\n\t\t\tresult.length > 0 ?\n\t\t\t\tresult.map(([name, value]) => {\n\t\t\t\t\tvalidateHeaderName(name);\n\t\t\t\t\tvalidateHeaderValue(name, String(value));\n\t\t\t\t\treturn [String(name).toLowerCase(), String(value)];\n\t\t\t\t}) :\n\t\t\t\tundefined;\n\n\t\tsuper(result);\n\n\t\t// Returning a Proxy that will lowercase key names, validate parameters and sort keys\n\t\t// eslint-disable-next-line no-constructor-return\n\t\treturn new Proxy(this, {\n\t\t\tget(target, p, receiver) {\n\t\t\t\tswitch (p) {\n\t\t\t\t\tcase 'append':\n\t\t\t\t\tcase 'set':\n\t\t\t\t\t\treturn (name, value) => {\n\t\t\t\t\t\t\tvalidateHeaderName(name);\n\t\t\t\t\t\t\tvalidateHeaderValue(name, String(value));\n\t\t\t\t\t\t\treturn URLSearchParams.prototype[p].call(\n\t\t\t\t\t\t\t\ttarget,\n\t\t\t\t\t\t\t\tString(name).toLowerCase(),\n\t\t\t\t\t\t\t\tString(value)\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t};\n\n\t\t\t\t\tcase 'delete':\n\t\t\t\t\tcase 'has':\n\t\t\t\t\tcase 'getAll':\n\t\t\t\t\t\treturn name => {\n\t\t\t\t\t\t\tvalidateHeaderName(name);\n\t\t\t\t\t\t\treturn URLSearchParams.prototype[p].call(\n\t\t\t\t\t\t\t\ttarget,\n\t\t\t\t\t\t\t\tString(name).toLowerCase()\n\t\t\t\t\t\t\t);\n\t\t\t\t\t\t};\n\n\t\t\t\t\tcase 'keys':\n\t\t\t\t\t\treturn () => {\n\t\t\t\t\t\t\ttarget.sort();\n\t\t\t\t\t\t\treturn new Set(URLSearchParams.prototype.keys.call(target)).keys();\n\t\t\t\t\t\t};\n\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn Reflect.get(target, p, receiver);\n\t\t\t\t}\n\t\t\t}\n\t\t});\n\t\t/* c8 ignore next */\n\t}\n\n\tget [Symbol.toStringTag]() {\n\t\treturn this.constructor.name;\n\t}\n\n\ttoString() {\n\t\treturn Object.prototype.toString.call(this);\n\t}\n\n\tget(name) {\n\t\tconst values = this.getAll(name);\n\t\tif (values.length === 0) {\n\t\t\treturn null;\n\t\t}\n\n\t\tlet value = values.join(', ');\n\t\tif (/^content-encoding$/i.test(name)) {\n\t\t\tvalue = value.toLowerCase();\n\t\t}\n\n\t\treturn value;\n\t}\n\n\tforEach(callback, thisArg = undefined) {\n\t\tfor (const name of this.keys()) {\n\t\t\tReflect.apply(callback, thisArg, [this.get(name), name, this]);\n\t\t}\n\t}\n\n\t* values() {\n\t\tfor (const name of this.keys()) {\n\t\t\tyield this.get(name);\n\t\t}\n\t}\n\n\t/**\n\t * @type {() => IterableIterator<[string, string]>}\n\t */\n\t* entries() {\n\t\tfor (const name of this.keys()) {\n\t\t\tyield [name, this.get(name)];\n\t\t}\n\t}\n\n\t[Symbol.iterator]() {\n\t\treturn this.entries();\n\t}\n\n\t/**\n\t * Node-fetch non-spec method\n\t * returning all headers and their values as array\n\t * @returns {Record<string, string[]>}\n\t */\n\traw() {\n\t\treturn [...this.keys()].reduce((result, key) => {\n\t\t\tresult[key] = this.getAll(key);\n\t\t\treturn result;\n\t\t}, {});\n\t}\n\n\t/**\n\t * For better console.log(headers) and also to convert Headers into Node.js Request compatible format\n\t */\n\t[Symbol.for('nodejs.util.inspect.custom')]() {\n\t\treturn [...this.keys()].reduce((result, key) => {\n\t\t\tconst values = this.getAll(key);\n\t\t\t// Http.request() only supports string as Host header.\n\t\t\t// This hack makes specifying custom Host header possible.\n\t\t\tif (key === 'host') {\n\t\t\t\tresult[key] = values[0];\n\t\t\t} else {\n\t\t\t\tresult[key] = values.length > 1 ? values : values[0];\n\t\t\t}\n\n\t\t\treturn result;\n\t\t}, {});\n\t}\n}\n\n/**\n * Re-shaping object for Web IDL tests\n * Only need to do it for overridden methods\n */\nObject.defineProperties(\n\tHeaders.prototype,\n\t['get', 'entries', 'forEach', 'values'].reduce((result, property) => {\n\t\tresult[property] = {enumerable: true};\n\t\treturn result;\n\t}, {})\n);\n\n/**\n * Create a Headers object from an http.IncomingMessage.rawHeaders, ignoring those that do\n * not conform to HTTP grammar productions.\n * @param {import('http').IncomingMessage['rawHeaders']} headers\n */\nexport function fromRawHeaders(headers = []) {\n\treturn new Headers(\n\t\theaders\n\t\t\t// Split into pairs\n\t\t\t.reduce((result, value, index, array) => {\n\t\t\t\tif (index % 2 === 0) {\n\t\t\t\t\tresult.push(array.slice(index, index + 2));\n\t\t\t\t}\n\n\t\t\t\treturn result;\n\t\t\t}, [])\n\t\t\t.filter(([name, value]) => {\n\t\t\t\ttry {\n\t\t\t\t\tvalidateHeaderName(name);\n\t\t\t\t\tvalidateHeaderValue(name, String(value));\n\t\t\t\t\treturn true;\n\t\t\t\t} catch {\n\t\t\t\t\treturn false;\n\t\t\t\t}\n\t\t\t})\n\n\t);\n}\n", "const redirectStatus = new Set([301, 302, 303, 307, 308]);\n\n/**\n * Redirect code matching\n *\n * @param {number} code - Status code\n * @return {boolean}\n */\nexport const isRedirect = code => {\n\treturn redirectStatus.has(code);\n};\n", "/**\n * Response.js\n *\n * Response class provides content decoding\n */\n\nimport Headers from './headers.js';\nimport Body, {clone, extractContentType} from './body.js';\nimport {isRedirect} from './utils/is-redirect.js';\n\nconst INTERNALS = Symbol('Response internals');\n\n/**\n * Response class\n *\n * Ref: https://fetch.spec.whatwg.org/#response-class\n *\n * @param   Stream  body  Readable stream\n * @param   Object  opts  Response options\n * @return  Void\n */\nexport default class Response extends Body {\n\tconstructor(body = null, options = {}) {\n\t\tsuper(body, options);\n\n\t\t// eslint-disable-next-line no-eq-null, eqeqeq, no-negated-condition\n\t\tconst status = options.status != null ? options.status : 200;\n\n\t\tconst headers = new Headers(options.headers);\n\n\t\tif (body !== null && !headers.has('Content-Type')) {\n\t\t\tconst contentType = extractContentType(body, this);\n\t\t\tif (contentType) {\n\t\t\t\theaders.append('Content-Type', contentType);\n\t\t\t}\n\t\t}\n\n\t\tthis[INTERNALS] = {\n\t\t\ttype: 'default',\n\t\t\turl: options.url,\n\t\t\tstatus,\n\t\t\tstatusText: options.statusText || '',\n\t\t\theaders,\n\t\t\tcounter: options.counter,\n\t\t\thighWaterMark: options.highWaterMark\n\t\t};\n\t}\n\n\tget type() {\n\t\treturn this[INTERNALS].type;\n\t}\n\n\tget url() {\n\t\treturn this[INTERNALS].url || '';\n\t}\n\n\tget status() {\n\t\treturn this[INTERNALS].status;\n\t}\n\n\t/**\n\t * Convenience property representing if the request ended normally\n\t */\n\tget ok() {\n\t\treturn this[INTERNALS].status >= 200 && this[INTERNALS].status < 300;\n\t}\n\n\tget redirected() {\n\t\treturn this[INTERNALS].counter > 0;\n\t}\n\n\tget statusText() {\n\t\treturn this[INTERNALS].statusText;\n\t}\n\n\tget headers() {\n\t\treturn this[INTERNALS].headers;\n\t}\n\n\tget highWaterMark() {\n\t\treturn this[INTERNALS].highWaterMark;\n\t}\n\n\t/**\n\t * Clone this response\n\t *\n\t * @return  Response\n\t */\n\tclone() {\n\t\treturn new Response(clone(this, this.highWaterMark), {\n\t\t\ttype: this.type,\n\t\t\turl: this.url,\n\t\t\tstatus: this.status,\n\t\t\tstatusText: this.statusText,\n\t\t\theaders: this.headers,\n\t\t\tok: this.ok,\n\t\t\tredirected: this.redirected,\n\t\t\tsize: this.size,\n\t\t\thighWaterMark: this.highWaterMark\n\t\t});\n\t}\n\n\t/**\n\t * @param {string} url    The URL that the new response is to originate from.\n\t * @param {number} status An optional status code for the response (e.g., 302.)\n\t * @returns {Response}    A Response object.\n\t */\n\tstatic redirect(url, status = 302) {\n\t\tif (!isRedirect(status)) {\n\t\t\tthrow new RangeError('Failed to execute \"redirect\" on \"response\": Invalid status code');\n\t\t}\n\n\t\treturn new Response(null, {\n\t\t\theaders: {\n\t\t\t\tlocation: new URL(url).toString()\n\t\t\t},\n\t\t\tstatus\n\t\t});\n\t}\n\n\tstatic error() {\n\t\tconst response = new Response(null, {status: 0, statusText: ''});\n\t\tresponse[INTERNALS].type = 'error';\n\t\treturn response;\n\t}\n\n\tstatic json(data = undefined, init = {}) {\n\t\tconst body = JSON.stringify(data);\n\n\t\tif (body === undefined) {\n\t\t\tthrow new TypeError('data is not JSON serializable');\n\t\t}\n\n\t\tconst headers = new Headers(init && init.headers);\n\n\t\tif (!headers.has('content-type')) {\n\t\t\theaders.set('content-type', 'application/json');\n\t\t}\n\n\t\treturn new Response(body, {\n\t\t\t...init,\n\t\t\theaders\n\t\t});\n\t}\n\n\tget [Symbol.toStringTag]() {\n\t\treturn 'Response';\n\t}\n}\n\nObject.defineProperties(Response.prototype, {\n\ttype: {enumerable: true},\n\turl: {enumerable: true},\n\tstatus: {enumerable: true},\n\tok: {enumerable: true},\n\tredirected: {enumerable: true},\n\tstatusText: {enumerable: true},\n\theaders: {enumerable: true},\n\tclone: {enumerable: true}\n});\n", "/**\n * Request.js\n *\n * Request class contains server only options\n *\n * All spec algorithm step numbers are based on https://fetch.spec.whatwg.org/commit-snapshots/ae716822cb3a61843226cd090eefc6589446c1d2/.\n */\n\nimport {format as formatUrl} from 'node:url';\nimport {deprecate} from 'node:util';\nimport Headers from './headers.js';\nimport Body, {clone, extractContentType, getTotalBytes} from './body.js';\nimport {isAbortSignal} from './utils/is.js';\nimport {getSearch} from './utils/get-search.js';\nimport {\n\tvalidateReferrerPolicy, determineRequestsReferrer, DEFAULT_REFERRER_POLICY\n} from './utils/referrer.js';\n\nconst INTERNALS = Symbol('Request internals');\n\n/**\n * Check if `obj` is an instance of Request.\n *\n * @param  {*} object\n * @return {boolean}\n */\nconst isRequest = object => {\n\treturn (\n\t\ttypeof object === 'object' &&\n\t\ttypeof object[INTERNALS] === 'object'\n\t);\n};\n\nconst doBadDataWarn = deprecate(() => {},\n\t'.data is not a valid RequestInit property, use .body instead',\n\t'https://github.com/node-fetch/node-fetch/issues/1000 (request)');\n\n/**\n * Request class\n *\n * Ref: https://fetch.spec.whatwg.org/#request-class\n *\n * @param   Mixed   input  Url or Request instance\n * @param   Object  init   Custom options\n * @return  Void\n */\nexport default class Request extends Body {\n\tconstructor(input, init = {}) {\n\t\tlet parsedURL;\n\n\t\t// Normalize input and force URL to be encoded as UTF-8 (https://github.com/node-fetch/node-fetch/issues/245)\n\t\tif (isRequest(input)) {\n\t\t\tparsedURL = new URL(input.url);\n\t\t} else {\n\t\t\tparsedURL = new URL(input);\n\t\t\tinput = {};\n\t\t}\n\n\t\tif (parsedURL.username !== '' || parsedURL.password !== '') {\n\t\t\tthrow new TypeError(`${parsedURL} is an url with embedded credentials.`);\n\t\t}\n\n\t\tlet method = init.method || input.method || 'GET';\n\t\tif (/^(delete|get|head|options|post|put)$/i.test(method)) {\n\t\t\tmethod = method.toUpperCase();\n\t\t}\n\n\t\tif (!isRequest(init) && 'data' in init) {\n\t\t\tdoBadDataWarn();\n\t\t}\n\n\t\t// eslint-disable-next-line no-eq-null, eqeqeq\n\t\tif ((init.body != null || (isRequest(input) && input.body !== null)) &&\n\t\t\t(method === 'GET' || method === 'HEAD')) {\n\t\t\tthrow new TypeError('Request with GET/HEAD method cannot have body');\n\t\t}\n\n\t\tconst inputBody = init.body ?\n\t\t\tinit.body :\n\t\t\t(isRequest(input) && input.body !== null ?\n\t\t\t\tclone(input) :\n\t\t\t\tnull);\n\n\t\tsuper(inputBody, {\n\t\t\tsize: init.size || input.size || 0\n\t\t});\n\n\t\tconst headers = new Headers(init.headers || input.headers || {});\n\n\t\tif (inputBody !== null && !headers.has('Content-Type')) {\n\t\t\tconst contentType = extractContentType(inputBody, this);\n\t\t\tif (contentType) {\n\t\t\t\theaders.set('Content-Type', contentType);\n\t\t\t}\n\t\t}\n\n\t\tlet signal = isRequest(input) ?\n\t\t\tinput.signal :\n\t\t\tnull;\n\t\tif ('signal' in init) {\n\t\t\tsignal = init.signal;\n\t\t}\n\n\t\t// eslint-disable-next-line no-eq-null, eqeqeq\n\t\tif (signal != null && !isAbortSignal(signal)) {\n\t\t\tthrow new TypeError('Expected signal to be an instanceof AbortSignal or EventTarget');\n\t\t}\n\n\t\t// §5.4, Request constructor steps, step 15.1\n\t\t// eslint-disable-next-line no-eq-null, eqeqeq\n\t\tlet referrer = init.referrer == null ? input.referrer : init.referrer;\n\t\tif (referrer === '') {\n\t\t\t// §5.4, Request constructor steps, step 15.2\n\t\t\treferrer = 'no-referrer';\n\t\t} else if (referrer) {\n\t\t\t// §5.4, Request constructor steps, step 15.3.1, 15.3.2\n\t\t\tconst parsedReferrer = new URL(referrer);\n\t\t\t// §5.4, Request constructor steps, step 15.3.3, 15.3.4\n\t\t\treferrer = /^about:(\\/\\/)?client$/.test(parsedReferrer) ? 'client' : parsedReferrer;\n\t\t} else {\n\t\t\treferrer = undefined;\n\t\t}\n\n\t\tthis[INTERNALS] = {\n\t\t\tmethod,\n\t\t\tredirect: init.redirect || input.redirect || 'follow',\n\t\t\theaders,\n\t\t\tparsedURL,\n\t\t\tsignal,\n\t\t\treferrer\n\t\t};\n\n\t\t// Node-fetch-only options\n\t\tthis.follow = init.follow === undefined ? (input.follow === undefined ? 20 : input.follow) : init.follow;\n\t\tthis.compress = init.compress === undefined ? (input.compress === undefined ? true : input.compress) : init.compress;\n\t\tthis.counter = init.counter || input.counter || 0;\n\t\tthis.agent = init.agent || input.agent;\n\t\tthis.highWaterMark = init.highWaterMark || input.highWaterMark || 16384;\n\t\tthis.insecureHTTPParser = init.insecureHTTPParser || input.insecureHTTPParser || false;\n\n\t\t// §5.4, Request constructor steps, step 16.\n\t\t// Default is empty string per https://fetch.spec.whatwg.org/#concept-request-referrer-policy\n\t\tthis.referrerPolicy = init.referrerPolicy || input.referrerPolicy || '';\n\t}\n\n\t/** @returns {string} */\n\tget method() {\n\t\treturn this[INTERNALS].method;\n\t}\n\n\t/** @returns {string} */\n\tget url() {\n\t\treturn formatUrl(this[INTERNALS].parsedURL);\n\t}\n\n\t/** @returns {Headers} */\n\tget headers() {\n\t\treturn this[INTERNALS].headers;\n\t}\n\n\tget redirect() {\n\t\treturn this[INTERNALS].redirect;\n\t}\n\n\t/** @returns {AbortSignal} */\n\tget signal() {\n\t\treturn this[INTERNALS].signal;\n\t}\n\n\t// https://fetch.spec.whatwg.org/#dom-request-referrer\n\tget referrer() {\n\t\tif (this[INTERNALS].referrer === 'no-referrer') {\n\t\t\treturn '';\n\t\t}\n\n\t\tif (this[INTERNALS].referrer === 'client') {\n\t\t\treturn 'about:client';\n\t\t}\n\n\t\tif (this[INTERNALS].referrer) {\n\t\t\treturn this[INTERNALS].referrer.toString();\n\t\t}\n\n\t\treturn undefined;\n\t}\n\n\tget referrerPolicy() {\n\t\treturn this[INTERNALS].referrerPolicy;\n\t}\n\n\tset referrerPolicy(referrerPolicy) {\n\t\tthis[INTERNALS].referrerPolicy = validateReferrerPolicy(referrerPolicy);\n\t}\n\n\t/**\n\t * Clone this request\n\t *\n\t * @return  Request\n\t */\n\tclone() {\n\t\treturn new Request(this);\n\t}\n\n\tget [Symbol.toStringTag]() {\n\t\treturn 'Request';\n\t}\n}\n\nObject.defineProperties(Request.prototype, {\n\tmethod: {enumerable: true},\n\turl: {enumerable: true},\n\theaders: {enumerable: true},\n\tredirect: {enumerable: true},\n\tclone: {enumerable: true},\n\tsignal: {enumerable: true},\n\treferrer: {enumerable: true},\n\treferrerPolicy: {enumerable: true}\n});\n\n/**\n * Convert a Request to Node.js http request options.\n *\n * @param {Request} request - A Request instance\n * @return The options object to be passed to http.request\n */\nexport const getNodeRequestOptions = request => {\n\tconst {parsedURL} = request[INTERNALS];\n\tconst headers = new Headers(request[INTERNALS].headers);\n\n\t// Fetch step 1.3\n\tif (!headers.has('Accept')) {\n\t\theaders.set('Accept', '*/*');\n\t}\n\n\t// HTTP-network-or-cache fetch steps 2.4-2.7\n\tlet contentLengthValue = null;\n\tif (request.body === null && /^(post|put)$/i.test(request.method)) {\n\t\tcontentLengthValue = '0';\n\t}\n\n\tif (request.body !== null) {\n\t\tconst totalBytes = getTotalBytes(request);\n\t\t// Set Content-Length if totalBytes is a number (that is not NaN)\n\t\tif (typeof totalBytes === 'number' && !Number.isNaN(totalBytes)) {\n\t\t\tcontentLengthValue = String(totalBytes);\n\t\t}\n\t}\n\n\tif (contentLengthValue) {\n\t\theaders.set('Content-Length', contentLengthValue);\n\t}\n\n\t// 4.1. Main fetch, step 2.6\n\t// > If request's referrer policy is the empty string, then set request's referrer policy to the\n\t// > default referrer policy.\n\tif (request.referrerPolicy === '') {\n\t\trequest.referrerPolicy = DEFAULT_REFERRER_POLICY;\n\t}\n\n\t// 4.1. Main fetch, step 2.7\n\t// > If request's referrer is not \"no-referrer\", set request's referrer to the result of invoking\n\t// > determine request's referrer.\n\tif (request.referrer && request.referrer !== 'no-referrer') {\n\t\trequest[INTERNALS].referrer = determineRequestsReferrer(request);\n\t} else {\n\t\trequest[INTERNALS].referrer = 'no-referrer';\n\t}\n\n\t// 4.5. HTTP-network-or-cache fetch, step 6.9\n\t// > If httpRequest's referrer is a URL, then append `Referer`/httpRequest's referrer, serialized\n\t// >  and isomorphic encoded, to httpRequest's header list.\n\tif (request[INTERNALS].referrer instanceof URL) {\n\t\theaders.set('Referer', request.referrer);\n\t}\n\n\t// HTTP-network-or-cache fetch step 2.11\n\tif (!headers.has('User-Agent')) {\n\t\theaders.set('User-Agent', 'node-fetch');\n\t}\n\n\t// HTTP-network-or-cache fetch step 2.15\n\tif (request.compress && !headers.has('Accept-Encoding')) {\n\t\theaders.set('Accept-Encoding', 'gzip, deflate, br');\n\t}\n\n\tlet {agent} = request;\n\tif (typeof agent === 'function') {\n\t\tagent = agent(parsedURL);\n\t}\n\n\t// HTTP-network fetch step 4.2\n\t// chunked encoding is handled by Node.js\n\n\tconst search = getSearch(parsedURL);\n\n\t// Pass the full URL directly to request(), but overwrite the following\n\t// options:\n\tconst options = {\n\t\t// Overwrite search to retain trailing ? (issue #776)\n\t\tpath: parsedURL.pathname + search,\n\t\t// The following options are not expressed in the URL\n\t\tmethod: request.method,\n\t\theaders: headers[Symbol.for('nodejs.util.inspect.custom')](),\n\t\tinsecureHTTPParser: request.insecureHTTPParser,\n\t\tagent\n\t};\n\n\treturn {\n\t\t/** @type {URL} */\n\t\tparsedURL,\n\t\toptions\n\t};\n};\n", "export const getSearch = parsedURL => {\n\tif (parsedURL.search) {\n\t\treturn parsedURL.search;\n\t}\n\n\tconst lastOffset = parsedURL.href.length - 1;\n\tconst hash = parsedURL.hash || (parsedURL.href[lastOffset] === '#' ? '#' : '');\n\treturn parsedURL.href[lastOffset - hash.length] === '?' ? '?' : '';\n};\n", "import {isIP} from 'node:net';\n\n/**\n * @external URL\n * @see {@link https://developer.mozilla.org/en-US/docs/Web/API/URL|URL}\n */\n\n/**\n * @module utils/referrer\n * @private\n */\n\n/**\n * @see {@link https://w3c.github.io/webappsec-referrer-policy/#strip-url|Referrer Policy §8.4. Strip url for use as a referrer}\n * @param {string} URL\n * @param {boolean} [originOnly=false]\n */\nexport function stripURLForUseAsAReferrer(url, originOnly = false) {\n\t// 1. If url is null, return no referrer.\n\tif (url == null) { // eslint-disable-line no-eq-null, eqeqeq\n\t\treturn 'no-referrer';\n\t}\n\n\turl = new URL(url);\n\n\t// 2. If url's scheme is a local scheme, then return no referrer.\n\tif (/^(about|blob|data):$/.test(url.protocol)) {\n\t\treturn 'no-referrer';\n\t}\n\n\t// 3. Set url's username to the empty string.\n\turl.username = '';\n\n\t// 4. Set url's password to null.\n\t// Note: `null` appears to be a mistake as this actually results in the password being `\"null\"`.\n\turl.password = '';\n\n\t// 5. Set url's fragment to null.\n\t// Note: `null` appears to be a mistake as this actually results in the fragment being `\"#null\"`.\n\turl.hash = '';\n\n\t// 6. If the origin-only flag is true, then:\n\tif (originOnly) {\n\t\t// 6.1. Set url's path to null.\n\t\t// Note: `null` appears to be a mistake as this actually results in the path being `\"/null\"`.\n\t\turl.pathname = '';\n\n\t\t// 6.2. Set url's query to null.\n\t\t// Note: `null` appears to be a mistake as this actually results in the query being `\"?null\"`.\n\t\turl.search = '';\n\t}\n\n\t// 7. Return url.\n\treturn url;\n}\n\n/**\n * @see {@link https://w3c.github.io/webappsec-referrer-policy/#enumdef-referrerpolicy|enum ReferrerPolicy}\n */\nexport const ReferrerPolicy = new Set([\n\t'',\n\t'no-referrer',\n\t'no-referrer-when-downgrade',\n\t'same-origin',\n\t'origin',\n\t'strict-origin',\n\t'origin-when-cross-origin',\n\t'strict-origin-when-cross-origin',\n\t'unsafe-url'\n]);\n\n/**\n * @see {@link https://w3c.github.io/webappsec-referrer-policy/#default-referrer-policy|default referrer policy}\n */\nexport const DEFAULT_REFERRER_POLICY = 'strict-origin-when-cross-origin';\n\n/**\n * @see {@link https://w3c.github.io/webappsec-referrer-policy/#referrer-policies|Referrer Policy §3. Referrer Policies}\n * @param {string} referrerPolicy\n * @returns {string} referrerPolicy\n */\nexport function validateReferrerPolicy(referrerPolicy) {\n\tif (!ReferrerPolicy.has(referrerPolicy)) {\n\t\tthrow new TypeError(`Invalid referrerPolicy: ${referrerPolicy}`);\n\t}\n\n\treturn referrerPolicy;\n}\n\n/**\n * @see {@link https://w3c.github.io/webappsec-secure-contexts/#is-origin-trustworthy|Referrer Policy §3.2. Is origin potentially trustworthy?}\n * @param {external:URL} url\n * @returns `true`: \"Potentially Trustworthy\", `false`: \"Not Trustworthy\"\n */\nexport function isOriginPotentiallyTrustworthy(url) {\n\t// 1. If origin is an opaque origin, return \"Not Trustworthy\".\n\t// Not applicable\n\n\t// 2. Assert: origin is a tuple origin.\n\t// Not for implementations\n\n\t// 3. If origin's scheme is either \"https\" or \"wss\", return \"Potentially Trustworthy\".\n\tif (/^(http|ws)s:$/.test(url.protocol)) {\n\t\treturn true;\n\t}\n\n\t// 4. If origin's host component matches one of the CIDR notations *********/8 or ::1/128 [RFC4632], return \"Potentially Trustworthy\".\n\tconst hostIp = url.host.replace(/(^\\[)|(]$)/g, '');\n\tconst hostIPVersion = isIP(hostIp);\n\n\tif (hostIPVersion === 4 && /^127\\./.test(hostIp)) {\n\t\treturn true;\n\t}\n\n\tif (hostIPVersion === 6 && /^(((0+:){7})|(::(0+:){0,6}))0*1$/.test(hostIp)) {\n\t\treturn true;\n\t}\n\n\t// 5. If origin's host component is \"localhost\" or falls within \".localhost\", and the user agent conforms to the name resolution rules in [let-localhost-be-localhost], return \"Potentially Trustworthy\".\n\t// We are returning FALSE here because we cannot ensure conformance to\n\t// let-localhost-be-loalhost (https://tools.ietf.org/html/draft-west-let-localhost-be-localhost)\n\tif (url.host === 'localhost' || url.host.endsWith('.localhost')) {\n\t\treturn false;\n\t}\n\n\t// 6. If origin's scheme component is file, return \"Potentially Trustworthy\".\n\tif (url.protocol === 'file:') {\n\t\treturn true;\n\t}\n\n\t// 7. If origin's scheme component is one which the user agent considers to be authenticated, return \"Potentially Trustworthy\".\n\t// Not supported\n\n\t// 8. If origin has been configured as a trustworthy origin, return \"Potentially Trustworthy\".\n\t// Not supported\n\n\t// 9. Return \"Not Trustworthy\".\n\treturn false;\n}\n\n/**\n * @see {@link https://w3c.github.io/webappsec-secure-contexts/#is-url-trustworthy|Referrer Policy §3.3. Is url potentially trustworthy?}\n * @param {external:URL} url\n * @returns `true`: \"Potentially Trustworthy\", `false`: \"Not Trustworthy\"\n */\nexport function isUrlPotentiallyTrustworthy(url) {\n\t// 1. If url is \"about:blank\" or \"about:srcdoc\", return \"Potentially Trustworthy\".\n\tif (/^about:(blank|srcdoc)$/.test(url)) {\n\t\treturn true;\n\t}\n\n\t// 2. If url's scheme is \"data\", return \"Potentially Trustworthy\".\n\tif (url.protocol === 'data:') {\n\t\treturn true;\n\t}\n\n\t// Note: The origin of blob: and filesystem: URLs is the origin of the context in which they were\n\t// created. Therefore, blobs created in a trustworthy origin will themselves be potentially\n\t// trustworthy.\n\tif (/^(blob|filesystem):$/.test(url.protocol)) {\n\t\treturn true;\n\t}\n\n\t// 3. Return the result of executing §3.2 Is origin potentially trustworthy? on url's origin.\n\treturn isOriginPotentiallyTrustworthy(url);\n}\n\n/**\n * Modifies the referrerURL to enforce any extra security policy considerations.\n * @see {@link https://w3c.github.io/webappsec-referrer-policy/#determine-requests-referrer|Referrer Policy §8.3. Determine request's Referrer}, step 7\n * @callback module:utils/referrer~referrerURLCallback\n * @param {external:URL} referrerURL\n * @returns {external:URL} modified referrerURL\n */\n\n/**\n * Modifies the referrerOrigin to enforce any extra security policy considerations.\n * @see {@link https://w3c.github.io/webappsec-referrer-policy/#determine-requests-referrer|Referrer Policy §8.3. Determine request's Referrer}, step 7\n * @callback module:utils/referrer~referrerOriginCallback\n * @param {external:URL} referrerOrigin\n * @returns {external:URL} modified referrerOrigin\n */\n\n/**\n * @see {@link https://w3c.github.io/webappsec-referrer-policy/#determine-requests-referrer|Referrer Policy §8.3. Determine request's Referrer}\n * @param {Request} request\n * @param {object} o\n * @param {module:utils/referrer~referrerURLCallback} o.referrerURLCallback\n * @param {module:utils/referrer~referrerOriginCallback} o.referrerOriginCallback\n * @returns {external:URL} Request's referrer\n */\nexport function determineRequestsReferrer(request, {referrerURLCallback, referrerOriginCallback} = {}) {\n\t// There are 2 notes in the specification about invalid pre-conditions.  We return null, here, for\n\t// these cases:\n\t// > Note: If request's referrer is \"no-referrer\", Fetch will not call into this algorithm.\n\t// > Note: If request's referrer policy is the empty string, Fetch will not call into this\n\t// > algorithm.\n\tif (request.referrer === 'no-referrer' || request.referrerPolicy === '') {\n\t\treturn null;\n\t}\n\n\t// 1. Let policy be request's associated referrer policy.\n\tconst policy = request.referrerPolicy;\n\n\t// 2. Let environment be request's client.\n\t// not applicable to node.js\n\n\t// 3. Switch on request's referrer:\n\tif (request.referrer === 'about:client') {\n\t\treturn 'no-referrer';\n\t}\n\n\t// \"a URL\": Let referrerSource be request's referrer.\n\tconst referrerSource = request.referrer;\n\n\t// 4. Let request's referrerURL be the result of stripping referrerSource for use as a referrer.\n\tlet referrerURL = stripURLForUseAsAReferrer(referrerSource);\n\n\t// 5. Let referrerOrigin be the result of stripping referrerSource for use as a referrer, with the\n\t//    origin-only flag set to true.\n\tlet referrerOrigin = stripURLForUseAsAReferrer(referrerSource, true);\n\n\t// 6. If the result of serializing referrerURL is a string whose length is greater than 4096, set\n\t//    referrerURL to referrerOrigin.\n\tif (referrerURL.toString().length > 4096) {\n\t\treferrerURL = referrerOrigin;\n\t}\n\n\t// 7. The user agent MAY alter referrerURL or referrerOrigin at this point to enforce arbitrary\n\t//    policy considerations in the interests of minimizing data leakage. For example, the user\n\t//    agent could strip the URL down to an origin, modify its host, replace it with an empty\n\t//    string, etc.\n\tif (referrerURLCallback) {\n\t\treferrerURL = referrerURLCallback(referrerURL);\n\t}\n\n\tif (referrerOriginCallback) {\n\t\treferrerOrigin = referrerOriginCallback(referrerOrigin);\n\t}\n\n\t// 8.Execute the statements corresponding to the value of policy:\n\tconst currentURL = new URL(request.url);\n\n\tswitch (policy) {\n\t\tcase 'no-referrer':\n\t\t\treturn 'no-referrer';\n\n\t\tcase 'origin':\n\t\t\treturn referrerOrigin;\n\n\t\tcase 'unsafe-url':\n\t\t\treturn referrerURL;\n\n\t\tcase 'strict-origin':\n\t\t\t// 1. If referrerURL is a potentially trustworthy URL and request's current URL is not a\n\t\t\t//    potentially trustworthy URL, then return no referrer.\n\t\t\tif (isUrlPotentiallyTrustworthy(referrerURL) && !isUrlPotentiallyTrustworthy(currentURL)) {\n\t\t\t\treturn 'no-referrer';\n\t\t\t}\n\n\t\t\t// 2. Return referrerOrigin.\n\t\t\treturn referrerOrigin.toString();\n\n\t\tcase 'strict-origin-when-cross-origin':\n\t\t\t// 1. If the origin of referrerURL and the origin of request's current URL are the same, then\n\t\t\t//    return referrerURL.\n\t\t\tif (referrerURL.origin === currentURL.origin) {\n\t\t\t\treturn referrerURL;\n\t\t\t}\n\n\t\t\t// 2. If referrerURL is a potentially trustworthy URL and request's current URL is not a\n\t\t\t//    potentially trustworthy URL, then return no referrer.\n\t\t\tif (isUrlPotentiallyTrustworthy(referrerURL) && !isUrlPotentiallyTrustworthy(currentURL)) {\n\t\t\t\treturn 'no-referrer';\n\t\t\t}\n\n\t\t\t// 3. Return referrerOrigin.\n\t\t\treturn referrerOrigin;\n\n\t\tcase 'same-origin':\n\t\t\t// 1. If the origin of referrerURL and the origin of request's current URL are the same, then\n\t\t\t//    return referrerURL.\n\t\t\tif (referrerURL.origin === currentURL.origin) {\n\t\t\t\treturn referrerURL;\n\t\t\t}\n\n\t\t\t// 2. Return no referrer.\n\t\t\treturn 'no-referrer';\n\n\t\tcase 'origin-when-cross-origin':\n\t\t\t// 1. If the origin of referrerURL and the origin of request's current URL are the same, then\n\t\t\t//    return referrerURL.\n\t\t\tif (referrerURL.origin === currentURL.origin) {\n\t\t\t\treturn referrerURL;\n\t\t\t}\n\n\t\t\t// Return referrerOrigin.\n\t\t\treturn referrerOrigin;\n\n\t\tcase 'no-referrer-when-downgrade':\n\t\t\t// 1. If referrerURL is a potentially trustworthy URL and request's current URL is not a\n\t\t\t//    potentially trustworthy URL, then return no referrer.\n\t\t\tif (isUrlPotentiallyTrustworthy(referrerURL) && !isUrlPotentiallyTrustworthy(currentURL)) {\n\t\t\t\treturn 'no-referrer';\n\t\t\t}\n\n\t\t\t// 2. Return referrerURL.\n\t\t\treturn referrerURL;\n\n\t\tdefault:\n\t\t\tthrow new TypeError(`Invalid referrerPolicy: ${policy}`);\n\t}\n}\n\n/**\n * @see {@link https://w3c.github.io/webappsec-referrer-policy/#parse-referrer-policy-from-header|Referrer Policy §8.1. Parse a referrer policy from a Referrer-Policy header}\n * @param {Headers} headers Response headers\n * @returns {string} policy\n */\nexport function parseReferrerPolicyFromHeader(headers) {\n\t// 1. Let policy-tokens be the result of extracting header list values given `Referrer-Policy`\n\t//    and response’s header list.\n\tconst policyTokens = (headers.get('referrer-policy') || '').split(/[,\\s]+/);\n\n\t// 2. Let policy be the empty string.\n\tlet policy = '';\n\n\t// 3. For each token in policy-tokens, if token is a referrer policy and token is not the empty\n\t//    string, then set policy to token.\n\t// Note: This algorithm loops over multiple policy values to allow deployment of new policy\n\t// values with fallbacks for older user agents, as described in § 11.1 Unknown Policy Values.\n\tfor (const token of policyTokens) {\n\t\tif (token && ReferrerPolicy.has(token)) {\n\t\t\tpolicy = token;\n\t\t}\n\t}\n\n\t// 4. Return policy.\n\treturn policy;\n}\n", "import {FetchBaseError} from './base.js';\n\n/**\n * AbortError interface for cancelled requests\n */\nexport class AbortError extends FetchBaseError {\n\tconstructor(message, type = 'aborted') {\n\t\tsuper(message, type);\n\t}\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,gGAAgG,GAAG,mIAAmI;AAAA,QACrP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,kGAAkG,GAAG,mIAAmI;AAAA,QACvP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,gGAAgG,GAAG,mIAAmI;AAAA,QACrP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,oGAAoG,GAAG,mIAAmI;AAAA,QACzP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,oGAAoG,GAAG,mIAAmI;AAAA,QACzP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,gGAAgG,GAAG,mIAAmI;AAAA,QACrP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,8FAA8F,GAAG,mIAAmI;AAAA,QACnP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACXF;AAAA;AAAA,WAAO,UAAU,OAAO,OAAO,IAAI,MAAM,CAAC,GAAG;AAAA,MAC3C,IAAI,GAAG,KAAK;AACV,YACE,QAAQ,gBACR,QAAQ,eACR,QAAQ,iBACR,QAAQ,UACR;AACA,kBAAQ,KAAK,8FAA8F,GAAG,mIAAmI;AAAA,QACnP;AAAA,MACF;AAAA,IACF,CAAC,CAAC;AAAA;AAAA;;;ACHF,IAAAA,oBAAiB;AACjB,wBAAkB;AAClB,uBAAiB;AACjB,IAAAC,sBAAoD;AACpD,IAAAC,sBAAqB;;;ACCf,SAAU,gBAAgB,KAAW;AAC1C,MAAI,CAAC,UAAU,KAAK,GAAG,GAAG;AACzB,UAAM,IAAI,UACT,kEAAkE;;AAKpE,QAAM,IAAI,QAAQ,UAAU,EAAE;AAG9B,QAAM,aAAa,IAAI,QAAQ,GAAG;AAClC,MAAI,eAAe,MAAM,cAAc,GAAG;AACzC,UAAM,IAAI,UAAU,qBAAqB;;AAI1C,QAAM,OAAO,IAAI,UAAU,GAAG,UAAU,EAAE,MAAM,GAAG;AAEnD,MAAI,UAAU;AACd,MAAI,SAAS;AACb,QAAM,OAAO,KAAK,CAAC,KAAK;AACxB,MAAI,WAAW;AACf,WAAS,IAAI,GAAG,IAAI,KAAK,QAAQ,KAAK;AACrC,QAAI,KAAK,CAAC,MAAM,UAAU;AACzB,eAAS;eACA,KAAK,CAAC,GAAG;AAClB,kBAAY,IAAM,KAAK,CAAC,CAAC;AACzB,UAAI,KAAK,CAAC,EAAE,QAAQ,UAAU,MAAM,GAAG;AACtC,kBAAU,KAAK,CAAC,EAAE,UAAU,CAAC;;;;AAKhC,MAAI,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,QAAQ;AAChC,gBAAY;AACZ,cAAU;;AAIX,QAAM,WAAW,SAAS,WAAW;AACrC,QAAM,OAAO,SAAS,IAAI,UAAU,aAAa,CAAC,CAAC;AACnD,QAAM,SAAS,OAAO,KAAK,MAAM,QAAQ;AAGzC,SAAO,OAAO;AACd,SAAO,WAAW;AAGlB,SAAO,UAAU;AAEjB,SAAO;AACR;AAEA,IAAA,eAAe;;;AC5Df,yBAAkC;AAClC,uBAA0C;AAC1C,yBAAqB;;;ACTd,IAAM,iBAAN,cAA6B,MAAM;AAAA,EACzC,YAAY,SAAS,MAAM;AAC1B,UAAM,OAAO;AAEb,UAAM,kBAAkB,MAAM,KAAK,WAAW;AAE9C,SAAK,OAAO;AAAA,EACb;AAAA,EAEA,IAAI,OAAO;AACV,WAAO,KAAK,YAAY;AAAA,EACzB;AAAA,EAEA,KAAK,OAAO,WAAW,IAAI;AAC1B,WAAO,KAAK,YAAY;AAAA,EACzB;AACD;;;ACNO,IAAM,aAAN,cAAyB,eAAe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAM9C,YAAY,SAAS,MAAM,aAAa;AACvC,UAAM,SAAS,IAAI;AAEnB,QAAI,aAAa;AAEhB,WAAK,OAAO,KAAK,QAAQ,YAAY;AACrC,WAAK,iBAAiB,YAAY;AAAA,IACnC;AAAA,EACD;AACD;;;ACnBA,IAAM,OAAO,OAAO;AAQb,IAAM,wBAAwB,YAAU;AAC9C,SACC,OAAO,WAAW,YAClB,OAAO,OAAO,WAAW,cACzB,OAAO,OAAO,WAAW,cACzB,OAAO,OAAO,QAAQ,cACtB,OAAO,OAAO,WAAW,cACzB,OAAO,OAAO,QAAQ,cACtB,OAAO,OAAO,QAAQ,cACtB,OAAO,OAAO,SAAS,cACvB,OAAO,IAAI,MAAM;AAEnB;AAOO,IAAM,SAAS,YAAU;AAC/B,SACC,UACA,OAAO,WAAW,YAClB,OAAO,OAAO,gBAAgB,cAC9B,OAAO,OAAO,SAAS,YACvB,OAAO,OAAO,WAAW,cACzB,OAAO,OAAO,gBAAgB,cAC9B,gBAAgB,KAAK,OAAO,IAAI,CAAC;AAEnC;AAOO,IAAM,gBAAgB,YAAU;AACtC,SACC,OAAO,WAAW,aACjB,OAAO,IAAI,MAAM,iBACjB,OAAO,IAAI,MAAM;AAGpB;AAUO,IAAM,sBAAsB,CAAC,aAAa,aAAa;AAC7D,QAAM,OAAO,IAAI,IAAI,QAAQ,EAAE;AAC/B,QAAM,OAAO,IAAI,IAAI,WAAW,EAAE;AAElC,SAAO,SAAS,QAAQ,KAAK,SAAS,IAAI,IAAI,EAAE;AACjD;AASO,IAAM,iBAAiB,CAAC,aAAa,aAAa;AACxD,QAAM,OAAO,IAAI,IAAI,QAAQ,EAAE;AAC/B,QAAM,OAAO,IAAI,IAAI,WAAW,EAAE;AAElC,SAAO,SAAS;AACjB;;;AHpEA,IAAM,eAAW,4BAAU,mBAAAC,QAAO,QAAQ;AAC1C,IAAM,YAAY,OAAO,gBAAgB;AAWzC,IAAqB,OAArB,MAA0B;AAAA,EACzB,YAAY,MAAM;AAAA,IACjB,OAAO;AAAA,EACR,IAAI,CAAC,GAAG;AACP,QAAI,WAAW;AAEf,QAAI,SAAS,MAAM;AAElB,aAAO;AAAA,IACR,WAAW,sBAAsB,IAAI,GAAG;AAEvC,aAAO,0BAAO,KAAK,KAAK,SAAS,CAAC;AAAA,IACnC,WAAW,OAAO,IAAI,GAAG;AAAA,IAEzB,WAAW,0BAAO,SAAS,IAAI,GAAG;AAAA,IAElC,WAAW,uBAAM,iBAAiB,IAAI,GAAG;AAExC,aAAO,0BAAO,KAAK,IAAI;AAAA,IACxB,WAAW,YAAY,OAAO,IAAI,GAAG;AAEpC,aAAO,0BAAO,KAAK,KAAK,QAAQ,KAAK,YAAY,KAAK,UAAU;AAAA,IACjE,WAAW,gBAAgB,mBAAAA,SAAQ;AAAA,IAEnC,WAAW,gBAAgB,UAAU;AAEpC,aAAO,eAAe,IAAI;AAC1B,iBAAW,KAAK,KAAK,MAAM,GAAG,EAAE,CAAC;AAAA,IAClC,OAAO;AAGN,aAAO,0BAAO,KAAK,OAAO,IAAI,CAAC;AAAA,IAChC;AAEA,QAAI,SAAS;AAEb,QAAI,0BAAO,SAAS,IAAI,GAAG;AAC1B,eAAS,mBAAAA,QAAO,SAAS,KAAK,IAAI;AAAA,IACnC,WAAW,OAAO,IAAI,GAAG;AACxB,eAAS,mBAAAA,QAAO,SAAS,KAAK,KAAK,OAAO,CAAC;AAAA,IAC5C;AAEA,SAAK,SAAS,IAAI;AAAA,MACjB;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAW;AAAA,MACX,OAAO;AAAA,IACR;AACA,SAAK,OAAO;AAEZ,QAAI,gBAAgB,mBAAAA,SAAQ;AAC3B,WAAK,GAAG,SAAS,YAAU;AAC1B,cAAM,QAAQ,kBAAkB,iBAC/B,SACA,IAAI,WAAW,+CAA+C,KAAK,GAAG,KAAK,OAAO,OAAO,IAAI,UAAU,MAAM;AAC9G,aAAK,SAAS,EAAE,QAAQ;AAAA,MACzB,CAAC;AAAA,IACF;AAAA,EACD;AAAA,EAEA,IAAI,OAAO;AACV,WAAO,KAAK,SAAS,EAAE;AAAA,EACxB;AAAA,EAEA,IAAI,WAAW;AACd,WAAO,KAAK,SAAS,EAAE;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,cAAc;AACnB,UAAM,EAAC,QAAQ,YAAY,WAAU,IAAI,MAAM,YAAY,IAAI;AAC/D,WAAO,OAAO,MAAM,YAAY,aAAa,UAAU;AAAA,EACxD;AAAA,EAEA,MAAM,WAAW;AAChB,UAAM,KAAK,KAAK,QAAQ,IAAI,cAAc;AAE1C,QAAI,GAAG,WAAW,mCAAmC,GAAG;AACvD,YAAM,WAAW,IAAI,SAAS;AAC9B,YAAM,aAAa,IAAI,gBAAgB,MAAM,KAAK,KAAK,CAAC;AAExD,iBAAW,CAAC,MAAM,KAAK,KAAK,YAAY;AACvC,iBAAS,OAAO,MAAM,KAAK;AAAA,MAC5B;AAEA,aAAO;AAAA,IACR;AAEA,UAAM,EAAC,WAAU,IAAI,MAAM,OAAO,gCAA6B;AAC/D,WAAO,WAAW,KAAK,MAAM,EAAE;AAAA,EAChC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO;AACZ,UAAM,KAAM,KAAK,WAAW,KAAK,QAAQ,IAAI,cAAc,KAAO,KAAK,SAAS,EAAE,QAAQ,KAAK,SAAS,EAAE,KAAK,QAAS;AACxH,UAAM,MAAM,MAAM,KAAK,YAAY;AAEnC,WAAO,IAAI,mBAAK,CAAC,GAAG,GAAG;AAAA,MACtB,MAAM;AAAA,IACP,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO;AACZ,UAAM,OAAO,MAAM,KAAK,KAAK;AAC7B,WAAO,KAAK,MAAM,IAAI;AAAA,EACvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM,OAAO;AACZ,UAAM,SAAS,MAAM,YAAY,IAAI;AACrC,WAAO,IAAI,YAAY,EAAE,OAAO,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,SAAS;AACR,WAAO,YAAY,IAAI;AAAA,EACxB;AACD;AAEA,KAAK,UAAU,aAAS,4BAAU,KAAK,UAAU,QAAQ,sEAA0E,mBAAmB;AAGtJ,OAAO,iBAAiB,KAAK,WAAW;AAAA,EACvC,MAAM,EAAC,YAAY,KAAI;AAAA,EACvB,UAAU,EAAC,YAAY,KAAI;AAAA,EAC3B,aAAa,EAAC,YAAY,KAAI;AAAA,EAC9B,MAAM,EAAC,YAAY,KAAI;AAAA,EACvB,MAAM,EAAC,YAAY,KAAI;AAAA,EACvB,MAAM,EAAC,YAAY,KAAI;AAAA,EACvB,MAAM,EAAC,SAAK;AAAA,IAAU,MAAM;AAAA,IAAC;AAAA,IAC5B;AAAA,IACA;AAAA,EAAiE,EAAC;AACpE,CAAC;AASD,eAAe,YAAY,MAAM;AAChC,MAAI,KAAK,SAAS,EAAE,WAAW;AAC9B,UAAM,IAAI,UAAU,0BAA0B,KAAK,GAAG,EAAE;AAAA,EACzD;AAEA,OAAK,SAAS,EAAE,YAAY;AAE5B,MAAI,KAAK,SAAS,EAAE,OAAO;AAC1B,UAAM,KAAK,SAAS,EAAE;AAAA,EACvB;AAEA,QAAM,EAAC,KAAI,IAAI;AAGf,MAAI,SAAS,MAAM;AAClB,WAAO,0BAAO,MAAM,CAAC;AAAA,EACtB;AAGA,MAAI,EAAE,gBAAgB,mBAAAA,UAAS;AAC9B,WAAO,0BAAO,MAAM,CAAC;AAAA,EACtB;AAIA,QAAM,QAAQ,CAAC;AACf,MAAI,aAAa;AAEjB,MAAI;AACH,qBAAiB,SAAS,MAAM;AAC/B,UAAI,KAAK,OAAO,KAAK,aAAa,MAAM,SAAS,KAAK,MAAM;AAC3D,cAAM,QAAQ,IAAI,WAAW,mBAAmB,KAAK,GAAG,gBAAgB,KAAK,IAAI,IAAI,UAAU;AAC/F,aAAK,QAAQ,KAAK;AAClB,cAAM;AAAA,MACP;AAEA,oBAAc,MAAM;AACpB,YAAM,KAAK,KAAK;AAAA,IACjB;AAAA,EACD,SAAS,OAAO;AACf,UAAM,SAAS,iBAAiB,iBAAiB,QAAQ,IAAI,WAAW,+CAA+C,KAAK,GAAG,KAAK,MAAM,OAAO,IAAI,UAAU,KAAK;AACpK,UAAM;AAAA,EACP;AAEA,MAAI,KAAK,kBAAkB,QAAQ,KAAK,eAAe,UAAU,MAAM;AACtE,QAAI;AACH,UAAI,MAAM,MAAM,OAAK,OAAO,MAAM,QAAQ,GAAG;AAC5C,eAAO,0BAAO,KAAK,MAAM,KAAK,EAAE,CAAC;AAAA,MAClC;AAEA,aAAO,0BAAO,OAAO,OAAO,UAAU;AAAA,IACvC,SAAS,OAAO;AACf,YAAM,IAAI,WAAW,kDAAkD,KAAK,GAAG,KAAK,MAAM,OAAO,IAAI,UAAU,KAAK;AAAA,IACrH;AAAA,EACD,OAAO;AACN,UAAM,IAAI,WAAW,4DAA4D,KAAK,GAAG,EAAE;AAAA,EAC5F;AACD;AASO,IAAM,QAAQ,CAAC,UAAU,kBAAkB;AACjD,MAAI;AACJ,MAAI;AACJ,MAAI,EAAC,KAAI,IAAI,SAAS,SAAS;AAG/B,MAAI,SAAS,UAAU;AACtB,UAAM,IAAI,MAAM,oCAAoC;AAAA,EACrD;AAIA,MAAK,gBAAgB,mBAAAA,WAAY,OAAO,KAAK,gBAAgB,YAAa;AAEzE,SAAK,IAAI,+BAAY,EAAC,cAAa,CAAC;AACpC,SAAK,IAAI,+BAAY,EAAC,cAAa,CAAC;AACpC,SAAK,KAAK,EAAE;AACZ,SAAK,KAAK,EAAE;AAEZ,aAAS,SAAS,EAAE,SAAS;AAC7B,WAAO;AAAA,EACR;AAEA,SAAO;AACR;AAEA,IAAM,iCAA6B;AAAA,EAClC,UAAQ,KAAK,YAAY;AAAA,EACzB;AAAA,EACA;AACD;AAYO,IAAM,qBAAqB,CAAC,MAAM,YAAY;AAEpD,MAAI,SAAS,MAAM;AAClB,WAAO;AAAA,EACR;AAGA,MAAI,OAAO,SAAS,UAAU;AAC7B,WAAO;AAAA,EACR;AAGA,MAAI,sBAAsB,IAAI,GAAG;AAChC,WAAO;AAAA,EACR;AAGA,MAAI,OAAO,IAAI,GAAG;AACjB,WAAO,KAAK,QAAQ;AAAA,EACrB;AAGA,MAAI,0BAAO,SAAS,IAAI,KAAK,uBAAM,iBAAiB,IAAI,KAAK,YAAY,OAAO,IAAI,GAAG;AACtF,WAAO;AAAA,EACR;AAEA,MAAI,gBAAgB,UAAU;AAC7B,WAAO,iCAAiC,QAAQ,SAAS,EAAE,QAAQ;AAAA,EACpE;AAGA,MAAI,QAAQ,OAAO,KAAK,gBAAgB,YAAY;AACnD,WAAO,gCAAgC,2BAA2B,IAAI,CAAC;AAAA,EACxE;AAGA,MAAI,gBAAgB,mBAAAA,SAAQ;AAC3B,WAAO;AAAA,EACR;AAGA,SAAO;AACR;AAWO,IAAM,gBAAgB,aAAW;AACvC,QAAM,EAAC,KAAI,IAAI,QAAQ,SAAS;AAGhC,MAAI,SAAS,MAAM;AAClB,WAAO;AAAA,EACR;AAGA,MAAI,OAAO,IAAI,GAAG;AACjB,WAAO,KAAK;AAAA,EACb;AAGA,MAAI,0BAAO,SAAS,IAAI,GAAG;AAC1B,WAAO,KAAK;AAAA,EACb;AAGA,MAAI,QAAQ,OAAO,KAAK,kBAAkB,YAAY;AACrD,WAAO,KAAK,kBAAkB,KAAK,eAAe,IAAI,KAAK,cAAc,IAAI;AAAA,EAC9E;AAGA,SAAO;AACR;AASO,IAAM,gBAAgB,OAAO,MAAM,EAAC,KAAI,MAAM;AACpD,MAAI,SAAS,MAAM;AAElB,SAAK,IAAI;AAAA,EACV,OAAO;AAEN,UAAM,SAAS,MAAM,IAAI;AAAA,EAC1B;AACD;;;AItYA,IAAAC,oBAAoB;AACpB,uBAAiB;AAGjB,IAAM,qBAAqB,OAAO,iBAAAC,QAAK,uBAAuB,aAC7D,iBAAAA,QAAK,qBACL,UAAQ;AACP,MAAI,CAAC,0BAA0B,KAAK,IAAI,GAAG;AAC1C,UAAM,QAAQ,IAAI,UAAU,2CAA2C,IAAI,GAAG;AAC9E,WAAO,eAAe,OAAO,QAAQ,EAAC,OAAO,yBAAwB,CAAC;AACtE,UAAM;AAAA,EACP;AACD;AAGD,IAAM,sBAAsB,OAAO,iBAAAA,QAAK,wBAAwB,aAC/D,iBAAAA,QAAK,sBACL,CAAC,MAAM,UAAU;AAChB,MAAI,kCAAkC,KAAK,KAAK,GAAG;AAClD,UAAM,QAAQ,IAAI,UAAU,yCAAyC,IAAI,IAAI;AAC7E,WAAO,eAAe,OAAO,QAAQ,EAAC,OAAO,mBAAkB,CAAC;AAChE,UAAM;AAAA,EACP;AACD;AAcD,IAAqB,UAArB,MAAqB,iBAAgB,gBAAgB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOpD,YAAY,MAAM;AAGjB,QAAI,SAAS,CAAC;AACd,QAAI,gBAAgB,UAAS;AAC5B,YAAM,MAAM,KAAK,IAAI;AACrB,iBAAW,CAAC,MAAM,MAAM,KAAK,OAAO,QAAQ,GAAG,GAAG;AACjD,eAAO,KAAK,GAAG,OAAO,IAAI,WAAS,CAAC,MAAM,KAAK,CAAC,CAAC;AAAA,MAClD;AAAA,IACD,WAAW,QAAQ,MAAM;AAAA,IAEzB,WAAW,OAAO,SAAS,YAAY,CAAC,wBAAM,iBAAiB,IAAI,GAAG;AACrE,YAAM,SAAS,KAAK,OAAO,QAAQ;AAEnC,UAAI,UAAU,MAAM;AAEnB,eAAO,KAAK,GAAG,OAAO,QAAQ,IAAI,CAAC;AAAA,MACpC,OAAO;AACN,YAAI,OAAO,WAAW,YAAY;AACjC,gBAAM,IAAI,UAAU,+BAA+B;AAAA,QACpD;AAIA,iBAAS,CAAC,GAAG,IAAI,EACf,IAAI,UAAQ;AACZ,cACC,OAAO,SAAS,YAAY,wBAAM,iBAAiB,IAAI,GACtD;AACD,kBAAM,IAAI,UAAU,6CAA6C;AAAA,UAClE;AAEA,iBAAO,CAAC,GAAG,IAAI;AAAA,QAChB,CAAC,EAAE,IAAI,UAAQ;AACd,cAAI,KAAK,WAAW,GAAG;AACtB,kBAAM,IAAI,UAAU,6CAA6C;AAAA,UAClE;AAEA,iBAAO,CAAC,GAAG,IAAI;AAAA,QAChB,CAAC;AAAA,MACH;AAAA,IACD,OAAO;AACN,YAAM,IAAI,UAAU,sIAAyI;AAAA,IAC9J;AAGA,aACC,OAAO,SAAS,IACf,OAAO,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM;AAC7B,yBAAmB,IAAI;AACvB,0BAAoB,MAAM,OAAO,KAAK,CAAC;AACvC,aAAO,CAAC,OAAO,IAAI,EAAE,YAAY,GAAG,OAAO,KAAK,CAAC;AAAA,IAClD,CAAC,IACD;AAEF,UAAM,MAAM;AAIZ,WAAO,IAAI,MAAM,MAAM;AAAA,MACtB,IAAI,QAAQ,GAAG,UAAU;AACxB,gBAAQ,GAAG;AAAA,UACV,KAAK;AAAA,UACL,KAAK;AACJ,mBAAO,CAAC,MAAM,UAAU;AACvB,iCAAmB,IAAI;AACvB,kCAAoB,MAAM,OAAO,KAAK,CAAC;AACvC,qBAAO,gBAAgB,UAAU,CAAC,EAAE;AAAA,gBACnC;AAAA,gBACA,OAAO,IAAI,EAAE,YAAY;AAAA,gBACzB,OAAO,KAAK;AAAA,cACb;AAAA,YACD;AAAA,UAED,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK;AACJ,mBAAO,UAAQ;AACd,iCAAmB,IAAI;AACvB,qBAAO,gBAAgB,UAAU,CAAC,EAAE;AAAA,gBACnC;AAAA,gBACA,OAAO,IAAI,EAAE,YAAY;AAAA,cAC1B;AAAA,YACD;AAAA,UAED,KAAK;AACJ,mBAAO,MAAM;AACZ,qBAAO,KAAK;AACZ,qBAAO,IAAI,IAAI,gBAAgB,UAAU,KAAK,KAAK,MAAM,CAAC,EAAE,KAAK;AAAA,YAClE;AAAA,UAED;AACC,mBAAO,QAAQ,IAAI,QAAQ,GAAG,QAAQ;AAAA,QACxC;AAAA,MACD;AAAA,IACD,CAAC;AAAA,EAEF;AAAA,EAEA,KAAK,OAAO,WAAW,IAAI;AAC1B,WAAO,KAAK,YAAY;AAAA,EACzB;AAAA,EAEA,WAAW;AACV,WAAO,OAAO,UAAU,SAAS,KAAK,IAAI;AAAA,EAC3C;AAAA,EAEA,IAAI,MAAM;AACT,UAAM,SAAS,KAAK,OAAO,IAAI;AAC/B,QAAI,OAAO,WAAW,GAAG;AACxB,aAAO;AAAA,IACR;AAEA,QAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,QAAI,sBAAsB,KAAK,IAAI,GAAG;AACrC,cAAQ,MAAM,YAAY;AAAA,IAC3B;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,QAAQ,UAAU,UAAU,QAAW;AACtC,eAAW,QAAQ,KAAK,KAAK,GAAG;AAC/B,cAAQ,MAAM,UAAU,SAAS,CAAC,KAAK,IAAI,IAAI,GAAG,MAAM,IAAI,CAAC;AAAA,IAC9D;AAAA,EACD;AAAA,EAEA,CAAE,SAAS;AACV,eAAW,QAAQ,KAAK,KAAK,GAAG;AAC/B,YAAM,KAAK,IAAI,IAAI;AAAA,IACpB;AAAA,EACD;AAAA;AAAA;AAAA;AAAA,EAKA,CAAE,UAAU;AACX,eAAW,QAAQ,KAAK,KAAK,GAAG;AAC/B,YAAM,CAAC,MAAM,KAAK,IAAI,IAAI,CAAC;AAAA,IAC5B;AAAA,EACD;AAAA,EAEA,CAAC,OAAO,QAAQ,IAAI;AACnB,WAAO,KAAK,QAAQ;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,MAAM;AACL,WAAO,CAAC,GAAG,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAC/C,aAAO,GAAG,IAAI,KAAK,OAAO,GAAG;AAC7B,aAAO;AAAA,IACR,GAAG,CAAC,CAAC;AAAA,EACN;AAAA;AAAA;AAAA;AAAA,EAKA,CAAC,OAAO,IAAI,4BAA4B,CAAC,IAAI;AAC5C,WAAO,CAAC,GAAG,KAAK,KAAK,CAAC,EAAE,OAAO,CAAC,QAAQ,QAAQ;AAC/C,YAAM,SAAS,KAAK,OAAO,GAAG;AAG9B,UAAI,QAAQ,QAAQ;AACnB,eAAO,GAAG,IAAI,OAAO,CAAC;AAAA,MACvB,OAAO;AACN,eAAO,GAAG,IAAI,OAAO,SAAS,IAAI,SAAS,OAAO,CAAC;AAAA,MACpD;AAEA,aAAO;AAAA,IACR,GAAG,CAAC,CAAC;AAAA,EACN;AACD;AAMA,OAAO;AAAA,EACN,QAAQ;AAAA,EACR,CAAC,OAAO,WAAW,WAAW,QAAQ,EAAE,OAAO,CAAC,QAAQ,aAAa;AACpE,WAAO,QAAQ,IAAI,EAAC,YAAY,KAAI;AACpC,WAAO;AAAA,EACR,GAAG,CAAC,CAAC;AACN;AAOO,SAAS,eAAe,UAAU,CAAC,GAAG;AAC5C,SAAO,IAAI;AAAA,IACV,QAEE,OAAO,CAAC,QAAQ,OAAO,OAAO,UAAU;AACxC,UAAI,QAAQ,MAAM,GAAG;AACpB,eAAO,KAAK,MAAM,MAAM,OAAO,QAAQ,CAAC,CAAC;AAAA,MAC1C;AAEA,aAAO;AAAA,IACR,GAAG,CAAC,CAAC,EACJ,OAAO,CAAC,CAAC,MAAM,KAAK,MAAM;AAC1B,UAAI;AACH,2BAAmB,IAAI;AACvB,4BAAoB,MAAM,OAAO,KAAK,CAAC;AACvC,eAAO;AAAA,MACR,QAAQ;AACP,eAAO;AAAA,MACR;AAAA,IACD,CAAC;AAAA,EAEH;AACD;;;AC1QA,IAAM,iBAAiB,oBAAI,IAAI,CAAC,KAAK,KAAK,KAAK,KAAK,GAAG,CAAC;AAQjD,IAAM,aAAa,UAAQ;AACjC,SAAO,eAAe,IAAI,IAAI;AAC/B;;;ACAA,IAAMC,aAAY,OAAO,oBAAoB;AAW7C,IAAqB,WAArB,MAAqB,kBAAiB,KAAK;AAAA,EAC1C,YAAY,OAAO,MAAM,UAAU,CAAC,GAAG;AACtC,UAAM,MAAM,OAAO;AAGnB,UAAM,SAAS,QAAQ,UAAU,OAAO,QAAQ,SAAS;AAEzD,UAAM,UAAU,IAAI,QAAQ,QAAQ,OAAO;AAE3C,QAAI,SAAS,QAAQ,CAAC,QAAQ,IAAI,cAAc,GAAG;AAClD,YAAM,cAAc,mBAAmB,MAAM,IAAI;AACjD,UAAI,aAAa;AAChB,gBAAQ,OAAO,gBAAgB,WAAW;AAAA,MAC3C;AAAA,IACD;AAEA,SAAKA,UAAS,IAAI;AAAA,MACjB,MAAM;AAAA,MACN,KAAK,QAAQ;AAAA,MACb;AAAA,MACA,YAAY,QAAQ,cAAc;AAAA,MAClC;AAAA,MACA,SAAS,QAAQ;AAAA,MACjB,eAAe,QAAQ;AAAA,IACxB;AAAA,EACD;AAAA,EAEA,IAAI,OAAO;AACV,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA,EAEA,IAAI,MAAM;AACT,WAAO,KAAKA,UAAS,EAAE,OAAO;AAAA,EAC/B;AAAA,EAEA,IAAI,SAAS;AACZ,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA,EAKA,IAAI,KAAK;AACR,WAAO,KAAKA,UAAS,EAAE,UAAU,OAAO,KAAKA,UAAS,EAAE,SAAS;AAAA,EAClE;AAAA,EAEA,IAAI,aAAa;AAChB,WAAO,KAAKA,UAAS,EAAE,UAAU;AAAA,EAClC;AAAA,EAEA,IAAI,aAAa;AAChB,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA,EAEA,IAAI,UAAU;AACb,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA,EAEA,IAAI,gBAAgB;AACnB,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACP,WAAO,IAAI,UAAS,MAAM,MAAM,KAAK,aAAa,GAAG;AAAA,MACpD,MAAM,KAAK;AAAA,MACX,KAAK,KAAK;AAAA,MACV,QAAQ,KAAK;AAAA,MACb,YAAY,KAAK;AAAA,MACjB,SAAS,KAAK;AAAA,MACd,IAAI,KAAK;AAAA,MACT,YAAY,KAAK;AAAA,MACjB,MAAM,KAAK;AAAA,MACX,eAAe,KAAK;AAAA,IACrB,CAAC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,OAAO,SAAS,KAAK,SAAS,KAAK;AAClC,QAAI,CAAC,WAAW,MAAM,GAAG;AACxB,YAAM,IAAI,WAAW,iEAAiE;AAAA,IACvF;AAEA,WAAO,IAAI,UAAS,MAAM;AAAA,MACzB,SAAS;AAAA,QACR,UAAU,IAAI,IAAI,GAAG,EAAE,SAAS;AAAA,MACjC;AAAA,MACA;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,OAAO,QAAQ;AACd,UAAM,WAAW,IAAI,UAAS,MAAM,EAAC,QAAQ,GAAG,YAAY,GAAE,CAAC;AAC/D,aAASA,UAAS,EAAE,OAAO;AAC3B,WAAO;AAAA,EACR;AAAA,EAEA,OAAO,KAAK,OAAO,QAAW,OAAO,CAAC,GAAG;AACxC,UAAM,OAAO,KAAK,UAAU,IAAI;AAEhC,QAAI,SAAS,QAAW;AACvB,YAAM,IAAI,UAAU,+BAA+B;AAAA,IACpD;AAEA,UAAM,UAAU,IAAI,QAAQ,QAAQ,KAAK,OAAO;AAEhD,QAAI,CAAC,QAAQ,IAAI,cAAc,GAAG;AACjC,cAAQ,IAAI,gBAAgB,kBAAkB;AAAA,IAC/C;AAEA,WAAO,IAAI,UAAS,MAAM;AAAA,MACzB,GAAG;AAAA,MACH;AAAA,IACD,CAAC;AAAA,EACF;AAAA,EAEA,KAAK,OAAO,WAAW,IAAI;AAC1B,WAAO;AAAA,EACR;AACD;AAEA,OAAO,iBAAiB,SAAS,WAAW;AAAA,EAC3C,MAAM,EAAC,YAAY,KAAI;AAAA,EACvB,KAAK,EAAC,YAAY,KAAI;AAAA,EACtB,QAAQ,EAAC,YAAY,KAAI;AAAA,EACzB,IAAI,EAAC,YAAY,KAAI;AAAA,EACrB,YAAY,EAAC,YAAY,KAAI;AAAA,EAC7B,YAAY,EAAC,YAAY,KAAI;AAAA,EAC7B,SAAS,EAAC,YAAY,KAAI;AAAA,EAC1B,OAAO,EAAC,YAAY,KAAI;AACzB,CAAC;;;ACvJD,sBAAkC;AAClC,IAAAC,oBAAwB;;;ACTjB,IAAM,YAAY,eAAa;AACrC,MAAI,UAAU,QAAQ;AACrB,WAAO,UAAU;AAAA,EAClB;AAEA,QAAM,aAAa,UAAU,KAAK,SAAS;AAC3C,QAAM,OAAO,UAAU,SAAS,UAAU,KAAK,UAAU,MAAM,MAAM,MAAM;AAC3E,SAAO,UAAU,KAAK,aAAa,KAAK,MAAM,MAAM,MAAM,MAAM;AACjE;;;ACRA,sBAAmB;AAiBZ,SAAS,0BAA0B,KAAK,aAAa,OAAO;AAElE,MAAI,OAAO,MAAM;AAChB,WAAO;AAAA,EACR;AAEA,QAAM,IAAI,IAAI,GAAG;AAGjB,MAAI,uBAAuB,KAAK,IAAI,QAAQ,GAAG;AAC9C,WAAO;AAAA,EACR;AAGA,MAAI,WAAW;AAIf,MAAI,WAAW;AAIf,MAAI,OAAO;AAGX,MAAI,YAAY;AAGf,QAAI,WAAW;AAIf,QAAI,SAAS;AAAA,EACd;AAGA,SAAO;AACR;AAKO,IAAM,iBAAiB,oBAAI,IAAI;AAAA,EACrC;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACD,CAAC;AAKM,IAAM,0BAA0B;AAOhC,SAAS,uBAAuB,gBAAgB;AACtD,MAAI,CAAC,eAAe,IAAI,cAAc,GAAG;AACxC,UAAM,IAAI,UAAU,2BAA2B,cAAc,EAAE;AAAA,EAChE;AAEA,SAAO;AACR;AAOO,SAAS,+BAA+B,KAAK;AAQnD,MAAI,gBAAgB,KAAK,IAAI,QAAQ,GAAG;AACvC,WAAO;AAAA,EACR;AAGA,QAAM,SAAS,IAAI,KAAK,QAAQ,eAAe,EAAE;AACjD,QAAM,oBAAgB,sBAAK,MAAM;AAEjC,MAAI,kBAAkB,KAAK,SAAS,KAAK,MAAM,GAAG;AACjD,WAAO;AAAA,EACR;AAEA,MAAI,kBAAkB,KAAK,mCAAmC,KAAK,MAAM,GAAG;AAC3E,WAAO;AAAA,EACR;AAKA,MAAI,IAAI,SAAS,eAAe,IAAI,KAAK,SAAS,YAAY,GAAG;AAChE,WAAO;AAAA,EACR;AAGA,MAAI,IAAI,aAAa,SAAS;AAC7B,WAAO;AAAA,EACR;AASA,SAAO;AACR;AAOO,SAAS,4BAA4B,KAAK;AAEhD,MAAI,yBAAyB,KAAK,GAAG,GAAG;AACvC,WAAO;AAAA,EACR;AAGA,MAAI,IAAI,aAAa,SAAS;AAC7B,WAAO;AAAA,EACR;AAKA,MAAI,uBAAuB,KAAK,IAAI,QAAQ,GAAG;AAC9C,WAAO;AAAA,EACR;AAGA,SAAO,+BAA+B,GAAG;AAC1C;AA0BO,SAAS,0BAA0B,SAAS,EAAC,qBAAqB,uBAAsB,IAAI,CAAC,GAAG;AAMtG,MAAI,QAAQ,aAAa,iBAAiB,QAAQ,mBAAmB,IAAI;AACxE,WAAO;AAAA,EACR;AAGA,QAAM,SAAS,QAAQ;AAMvB,MAAI,QAAQ,aAAa,gBAAgB;AACxC,WAAO;AAAA,EACR;AAGA,QAAM,iBAAiB,QAAQ;AAG/B,MAAI,cAAc,0BAA0B,cAAc;AAI1D,MAAI,iBAAiB,0BAA0B,gBAAgB,IAAI;AAInE,MAAI,YAAY,SAAS,EAAE,SAAS,MAAM;AACzC,kBAAc;AAAA,EACf;AAMA,MAAI,qBAAqB;AACxB,kBAAc,oBAAoB,WAAW;AAAA,EAC9C;AAEA,MAAI,wBAAwB;AAC3B,qBAAiB,uBAAuB,cAAc;AAAA,EACvD;AAGA,QAAM,aAAa,IAAI,IAAI,QAAQ,GAAG;AAEtC,UAAQ,QAAQ;AAAA,IACf,KAAK;AACJ,aAAO;AAAA,IAER,KAAK;AACJ,aAAO;AAAA,IAER,KAAK;AACJ,aAAO;AAAA,IAER,KAAK;AAGJ,UAAI,4BAA4B,WAAW,KAAK,CAAC,4BAA4B,UAAU,GAAG;AACzF,eAAO;AAAA,MACR;AAGA,aAAO,eAAe,SAAS;AAAA,IAEhC,KAAK;AAGJ,UAAI,YAAY,WAAW,WAAW,QAAQ;AAC7C,eAAO;AAAA,MACR;AAIA,UAAI,4BAA4B,WAAW,KAAK,CAAC,4BAA4B,UAAU,GAAG;AACzF,eAAO;AAAA,MACR;AAGA,aAAO;AAAA,IAER,KAAK;AAGJ,UAAI,YAAY,WAAW,WAAW,QAAQ;AAC7C,eAAO;AAAA,MACR;AAGA,aAAO;AAAA,IAER,KAAK;AAGJ,UAAI,YAAY,WAAW,WAAW,QAAQ;AAC7C,eAAO;AAAA,MACR;AAGA,aAAO;AAAA,IAER,KAAK;AAGJ,UAAI,4BAA4B,WAAW,KAAK,CAAC,4BAA4B,UAAU,GAAG;AACzF,eAAO;AAAA,MACR;AAGA,aAAO;AAAA,IAER;AACC,YAAM,IAAI,UAAU,2BAA2B,MAAM,EAAE;AAAA,EACzD;AACD;AAOO,SAAS,8BAA8B,SAAS;AAGtD,QAAM,gBAAgB,QAAQ,IAAI,iBAAiB,KAAK,IAAI,MAAM,QAAQ;AAG1E,MAAI,SAAS;AAMb,aAAW,SAAS,cAAc;AACjC,QAAI,SAAS,eAAe,IAAI,KAAK,GAAG;AACvC,eAAS;AAAA,IACV;AAAA,EACD;AAGA,SAAO;AACR;;;AFjUA,IAAMC,aAAY,OAAO,mBAAmB;AAQ5C,IAAM,YAAY,YAAU;AAC3B,SACC,OAAO,WAAW,YAClB,OAAO,OAAOA,UAAS,MAAM;AAE/B;AAEA,IAAM,oBAAgB;AAAA,EAAU,MAAM;AAAA,EAAC;AAAA,EACtC;AAAA,EACA;AAAgE;AAWjE,IAAqB,UAArB,MAAqB,iBAAgB,KAAK;AAAA,EACzC,YAAY,OAAO,OAAO,CAAC,GAAG;AAC7B,QAAI;AAGJ,QAAI,UAAU,KAAK,GAAG;AACrB,kBAAY,IAAI,IAAI,MAAM,GAAG;AAAA,IAC9B,OAAO;AACN,kBAAY,IAAI,IAAI,KAAK;AACzB,cAAQ,CAAC;AAAA,IACV;AAEA,QAAI,UAAU,aAAa,MAAM,UAAU,aAAa,IAAI;AAC3D,YAAM,IAAI,UAAU,GAAG,SAAS,uCAAuC;AAAA,IACxE;AAEA,QAAI,SAAS,KAAK,UAAU,MAAM,UAAU;AAC5C,QAAI,wCAAwC,KAAK,MAAM,GAAG;AACzD,eAAS,OAAO,YAAY;AAAA,IAC7B;AAEA,QAAI,CAAC,UAAU,IAAI,KAAK,UAAU,MAAM;AACvC,oBAAc;AAAA,IACf;AAGA,SAAK,KAAK,QAAQ,QAAS,UAAU,KAAK,KAAK,MAAM,SAAS,UAC5D,WAAW,SAAS,WAAW,SAAS;AACzC,YAAM,IAAI,UAAU,+CAA+C;AAAA,IACpE;AAEA,UAAM,YAAY,KAAK,OACtB,KAAK,OACJ,UAAU,KAAK,KAAK,MAAM,SAAS,OACnC,MAAM,KAAK,IACX;AAEF,UAAM,WAAW;AAAA,MAChB,MAAM,KAAK,QAAQ,MAAM,QAAQ;AAAA,IAClC,CAAC;AAED,UAAM,UAAU,IAAI,QAAQ,KAAK,WAAW,MAAM,WAAW,CAAC,CAAC;AAE/D,QAAI,cAAc,QAAQ,CAAC,QAAQ,IAAI,cAAc,GAAG;AACvD,YAAM,cAAc,mBAAmB,WAAW,IAAI;AACtD,UAAI,aAAa;AAChB,gBAAQ,IAAI,gBAAgB,WAAW;AAAA,MACxC;AAAA,IACD;AAEA,QAAI,SAAS,UAAU,KAAK,IAC3B,MAAM,SACN;AACD,QAAI,YAAY,MAAM;AACrB,eAAS,KAAK;AAAA,IACf;AAGA,QAAI,UAAU,QAAQ,CAAC,cAAc,MAAM,GAAG;AAC7C,YAAM,IAAI,UAAU,gEAAgE;AAAA,IACrF;AAIA,QAAI,WAAW,KAAK,YAAY,OAAO,MAAM,WAAW,KAAK;AAC7D,QAAI,aAAa,IAAI;AAEpB,iBAAW;AAAA,IACZ,WAAW,UAAU;AAEpB,YAAM,iBAAiB,IAAI,IAAI,QAAQ;AAEvC,iBAAW,wBAAwB,KAAK,cAAc,IAAI,WAAW;AAAA,IACtE,OAAO;AACN,iBAAW;AAAA,IACZ;AAEA,SAAKA,UAAS,IAAI;AAAA,MACjB;AAAA,MACA,UAAU,KAAK,YAAY,MAAM,YAAY;AAAA,MAC7C;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACD;AAGA,SAAK,SAAS,KAAK,WAAW,SAAa,MAAM,WAAW,SAAY,KAAK,MAAM,SAAU,KAAK;AAClG,SAAK,WAAW,KAAK,aAAa,SAAa,MAAM,aAAa,SAAY,OAAO,MAAM,WAAY,KAAK;AAC5G,SAAK,UAAU,KAAK,WAAW,MAAM,WAAW;AAChD,SAAK,QAAQ,KAAK,SAAS,MAAM;AACjC,SAAK,gBAAgB,KAAK,iBAAiB,MAAM,iBAAiB;AAClE,SAAK,qBAAqB,KAAK,sBAAsB,MAAM,sBAAsB;AAIjF,SAAK,iBAAiB,KAAK,kBAAkB,MAAM,kBAAkB;AAAA,EACtE;AAAA;AAAA,EAGA,IAAI,SAAS;AACZ,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA;AAAA,EAGA,IAAI,MAAM;AACT,eAAO,gBAAAC,QAAU,KAAKD,UAAS,EAAE,SAAS;AAAA,EAC3C;AAAA;AAAA,EAGA,IAAI,UAAU;AACb,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA,EAEA,IAAI,WAAW;AACd,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA;AAAA,EAGA,IAAI,SAAS;AACZ,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA;AAAA,EAGA,IAAI,WAAW;AACd,QAAI,KAAKA,UAAS,EAAE,aAAa,eAAe;AAC/C,aAAO;AAAA,IACR;AAEA,QAAI,KAAKA,UAAS,EAAE,aAAa,UAAU;AAC1C,aAAO;AAAA,IACR;AAEA,QAAI,KAAKA,UAAS,EAAE,UAAU;AAC7B,aAAO,KAAKA,UAAS,EAAE,SAAS,SAAS;AAAA,IAC1C;AAEA,WAAO;AAAA,EACR;AAAA,EAEA,IAAI,iBAAiB;AACpB,WAAO,KAAKA,UAAS,EAAE;AAAA,EACxB;AAAA,EAEA,IAAI,eAAe,gBAAgB;AAClC,SAAKA,UAAS,EAAE,iBAAiB,uBAAuB,cAAc;AAAA,EACvE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,QAAQ;AACP,WAAO,IAAI,SAAQ,IAAI;AAAA,EACxB;AAAA,EAEA,KAAK,OAAO,WAAW,IAAI;AAC1B,WAAO;AAAA,EACR;AACD;AAEA,OAAO,iBAAiB,QAAQ,WAAW;AAAA,EAC1C,QAAQ,EAAC,YAAY,KAAI;AAAA,EACzB,KAAK,EAAC,YAAY,KAAI;AAAA,EACtB,SAAS,EAAC,YAAY,KAAI;AAAA,EAC1B,UAAU,EAAC,YAAY,KAAI;AAAA,EAC3B,OAAO,EAAC,YAAY,KAAI;AAAA,EACxB,QAAQ,EAAC,YAAY,KAAI;AAAA,EACzB,UAAU,EAAC,YAAY,KAAI;AAAA,EAC3B,gBAAgB,EAAC,YAAY,KAAI;AAClC,CAAC;AAQM,IAAM,wBAAwB,aAAW;AAC/C,QAAM,EAAC,UAAS,IAAI,QAAQA,UAAS;AACrC,QAAM,UAAU,IAAI,QAAQ,QAAQA,UAAS,EAAE,OAAO;AAGtD,MAAI,CAAC,QAAQ,IAAI,QAAQ,GAAG;AAC3B,YAAQ,IAAI,UAAU,KAAK;AAAA,EAC5B;AAGA,MAAI,qBAAqB;AACzB,MAAI,QAAQ,SAAS,QAAQ,gBAAgB,KAAK,QAAQ,MAAM,GAAG;AAClE,yBAAqB;AAAA,EACtB;AAEA,MAAI,QAAQ,SAAS,MAAM;AAC1B,UAAM,aAAa,cAAc,OAAO;AAExC,QAAI,OAAO,eAAe,YAAY,CAAC,OAAO,MAAM,UAAU,GAAG;AAChE,2BAAqB,OAAO,UAAU;AAAA,IACvC;AAAA,EACD;AAEA,MAAI,oBAAoB;AACvB,YAAQ,IAAI,kBAAkB,kBAAkB;AAAA,EACjD;AAKA,MAAI,QAAQ,mBAAmB,IAAI;AAClC,YAAQ,iBAAiB;AAAA,EAC1B;AAKA,MAAI,QAAQ,YAAY,QAAQ,aAAa,eAAe;AAC3D,YAAQA,UAAS,EAAE,WAAW,0BAA0B,OAAO;AAAA,EAChE,OAAO;AACN,YAAQA,UAAS,EAAE,WAAW;AAAA,EAC/B;AAKA,MAAI,QAAQA,UAAS,EAAE,oBAAoB,KAAK;AAC/C,YAAQ,IAAI,WAAW,QAAQ,QAAQ;AAAA,EACxC;AAGA,MAAI,CAAC,QAAQ,IAAI,YAAY,GAAG;AAC/B,YAAQ,IAAI,cAAc,YAAY;AAAA,EACvC;AAGA,MAAI,QAAQ,YAAY,CAAC,QAAQ,IAAI,iBAAiB,GAAG;AACxD,YAAQ,IAAI,mBAAmB,mBAAmB;AAAA,EACnD;AAEA,MAAI,EAAC,MAAK,IAAI;AACd,MAAI,OAAO,UAAU,YAAY;AAChC,YAAQ,MAAM,SAAS;AAAA,EACxB;AAKA,QAAM,SAAS,UAAU,SAAS;AAIlC,QAAM,UAAU;AAAA;AAAA,IAEf,MAAM,UAAU,WAAW;AAAA;AAAA,IAE3B,QAAQ,QAAQ;AAAA,IAChB,SAAS,QAAQ,OAAO,IAAI,4BAA4B,CAAC,EAAE;AAAA,IAC3D,oBAAoB,QAAQ;AAAA,IAC5B;AAAA,EACD;AAEA,SAAO;AAAA;AAAA,IAEN;AAAA,IACA;AAAA,EACD;AACD;;;AGnTO,IAAM,aAAN,cAAyB,eAAe;AAAA,EAC9C,YAAY,SAAS,OAAO,WAAW;AACtC,UAAM,SAAS,IAAI;AAAA,EACpB;AACD;;;AZ6BA,IAAM,mBAAmB,oBAAI,IAAI,CAAC,SAAS,SAAS,QAAQ,CAAC;AAS7D,eAAO,MAA6B,KAAK,UAAU;AAClD,SAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AAEvC,UAAM,UAAU,IAAI,QAAQ,KAAK,QAAQ;AACzC,UAAM,EAAC,WAAW,QAAO,IAAI,sBAAsB,OAAO;AAC1D,QAAI,CAAC,iBAAiB,IAAI,UAAU,QAAQ,GAAG;AAC9C,YAAM,IAAI,UAAU,0BAA0B,GAAG,iBAAiB,UAAU,SAAS,QAAQ,MAAM,EAAE,CAAC,qBAAqB;AAAA,IAC5H;AAEA,QAAI,UAAU,aAAa,SAAS;AACnC,YAAM,OAAO,aAAgB,QAAQ,GAAG;AACxC,YAAME,YAAW,IAAI,SAAS,MAAM,EAAC,SAAS,EAAC,gBAAgB,KAAK,SAAQ,EAAC,CAAC;AAC9E,cAAQA,SAAQ;AAChB;AAAA,IACD;AAGA,UAAM,QAAQ,UAAU,aAAa,WAAW,kBAAAC,UAAQ,kBAAAC,SAAM;AAC9D,UAAM,EAAC,OAAM,IAAI;AACjB,QAAI,WAAW;AAEf,UAAM,QAAQ,MAAM;AACnB,YAAM,QAAQ,IAAI,WAAW,4BAA4B;AACzD,aAAO,KAAK;AACZ,UAAI,QAAQ,QAAQ,QAAQ,gBAAgB,oBAAAC,QAAO,UAAU;AAC5D,gBAAQ,KAAK,QAAQ,KAAK;AAAA,MAC3B;AAEA,UAAI,CAAC,YAAY,CAAC,SAAS,MAAM;AAChC;AAAA,MACD;AAEA,eAAS,KAAK,KAAK,SAAS,KAAK;AAAA,IAClC;AAEA,QAAI,UAAU,OAAO,SAAS;AAC7B,YAAM;AACN;AAAA,IACD;AAEA,UAAM,mBAAmB,MAAM;AAC9B,YAAM;AACN,eAAS;AAAA,IACV;AAGA,UAAM,WAAW,KAAK,UAAU,SAAS,GAAG,OAAO;AAEnD,QAAI,QAAQ;AACX,aAAO,iBAAiB,SAAS,gBAAgB;AAAA,IAClD;AAEA,UAAM,WAAW,MAAM;AACtB,eAAS,MAAM;AACf,UAAI,QAAQ;AACX,eAAO,oBAAoB,SAAS,gBAAgB;AAAA,MACrD;AAAA,IACD;AAEA,aAAS,GAAG,SAAS,WAAS;AAC7B,aAAO,IAAI,WAAW,cAAc,QAAQ,GAAG,oBAAoB,MAAM,OAAO,IAAI,UAAU,KAAK,CAAC;AACpG,eAAS;AAAA,IACV,CAAC;AAED,wCAAoC,UAAU,WAAS;AACtD,UAAI,YAAY,SAAS,MAAM;AAC9B,iBAAS,KAAK,QAAQ,KAAK;AAAA,MAC5B;AAAA,IACD,CAAC;AAGD,QAAI,QAAQ,UAAU,OAAO;AAG5B,eAAS,GAAG,UAAU,OAAK;AAC1B,YAAI;AACJ,UAAE,gBAAgB,OAAO,MAAM;AAC9B,iCAAuB,EAAE;AAAA,QAC1B,CAAC;AACD,UAAE,gBAAgB,SAAS,cAAY;AAEtC,cAAI,YAAY,uBAAuB,EAAE,gBAAgB,CAAC,UAAU;AACnE,kBAAM,QAAQ,IAAI,MAAM,iBAAiB;AACzC,kBAAM,OAAO;AACb,qBAAS,KAAK,KAAK,SAAS,KAAK;AAAA,UAClC;AAAA,QACD,CAAC;AAAA,MACF,CAAC;AAAA,IACF;AAEA,aAAS,GAAG,YAAY,eAAa;AACpC,eAAS,WAAW,CAAC;AACrB,YAAM,UAAU,eAAe,UAAU,UAAU;AAGnD,UAAI,WAAW,UAAU,UAAU,GAAG;AAErC,cAAM,WAAW,QAAQ,IAAI,UAAU;AAGvC,YAAI,cAAc;AAClB,YAAI;AACH,wBAAc,aAAa,OAAO,OAAO,IAAI,IAAI,UAAU,QAAQ,GAAG;AAAA,QACvE,QAAQ;AAIP,cAAI,QAAQ,aAAa,UAAU;AAClC,mBAAO,IAAI,WAAW,wDAAwD,QAAQ,IAAI,kBAAkB,CAAC;AAC7G,qBAAS;AACT;AAAA,UACD;AAAA,QACD;AAGA,gBAAQ,QAAQ,UAAU;AAAA,UACzB,KAAK;AACJ,mBAAO,IAAI,WAAW,0EAA0E,QAAQ,GAAG,IAAI,aAAa,CAAC;AAC7H,qBAAS;AACT;AAAA,UACD,KAAK;AAEJ;AAAA,UACD,KAAK,UAAU;AAEd,gBAAI,gBAAgB,MAAM;AACzB;AAAA,YACD;AAGA,gBAAI,QAAQ,WAAW,QAAQ,QAAQ;AACtC,qBAAO,IAAI,WAAW,gCAAgC,QAAQ,GAAG,IAAI,cAAc,CAAC;AACpF,uBAAS;AACT;AAAA,YACD;AAIA,kBAAM,iBAAiB;AAAA,cACtB,SAAS,IAAI,QAAQ,QAAQ,OAAO;AAAA,cACpC,QAAQ,QAAQ;AAAA,cAChB,SAAS,QAAQ,UAAU;AAAA,cAC3B,OAAO,QAAQ;AAAA,cACf,UAAU,QAAQ;AAAA,cAClB,QAAQ,QAAQ;AAAA,cAChB,MAAM,MAAM,OAAO;AAAA,cACnB,QAAQ,QAAQ;AAAA,cAChB,MAAM,QAAQ;AAAA,cACd,UAAU,QAAQ;AAAA,cAClB,gBAAgB,QAAQ;AAAA,YACzB;AAWA,gBAAI,CAAC,oBAAoB,QAAQ,KAAK,WAAW,KAAK,CAAC,eAAe,QAAQ,KAAK,WAAW,GAAG;AAChG,yBAAW,QAAQ,CAAC,iBAAiB,oBAAoB,UAAU,SAAS,GAAG;AAC9E,+BAAe,QAAQ,OAAO,IAAI;AAAA,cACnC;AAAA,YACD;AAGA,gBAAI,UAAU,eAAe,OAAO,QAAQ,QAAQ,SAAS,gBAAgB,oBAAAA,QAAO,UAAU;AAC7F,qBAAO,IAAI,WAAW,4DAA4D,sBAAsB,CAAC;AACzG,uBAAS;AACT;AAAA,YACD;AAGA,gBAAI,UAAU,eAAe,QAAS,UAAU,eAAe,OAAO,UAAU,eAAe,QAAQ,QAAQ,WAAW,QAAS;AAClI,6BAAe,SAAS;AACxB,6BAAe,OAAO;AACtB,6BAAe,QAAQ,OAAO,gBAAgB;AAAA,YAC/C;AAGA,kBAAM,yBAAyB,8BAA8B,OAAO;AACpE,gBAAI,wBAAwB;AAC3B,6BAAe,iBAAiB;AAAA,YACjC;AAGA,oBAAQ,MAAM,IAAI,QAAQ,aAAa,cAAc,CAAC,CAAC;AACvD,qBAAS;AACT;AAAA,UACD;AAAA,UAEA;AACC,mBAAO,OAAO,IAAI,UAAU,oBAAoB,QAAQ,QAAQ,2CAA2C,CAAC;AAAA,QAC9G;AAAA,MACD;AAGA,UAAI,QAAQ;AACX,kBAAU,KAAK,OAAO,MAAM;AAC3B,iBAAO,oBAAoB,SAAS,gBAAgB;AAAA,QACrD,CAAC;AAAA,MACF;AAEA,UAAI,WAAO,oBAAAC,UAAK,WAAW,IAAI,gCAAY,GAAG,WAAS;AACtD,YAAI,OAAO;AACV,iBAAO,KAAK;AAAA,QACb;AAAA,MACD,CAAC;AAGD,UAAI,QAAQ,UAAU,UAAU;AAC/B,kBAAU,GAAG,WAAW,gBAAgB;AAAA,MACzC;AAEA,YAAM,kBAAkB;AAAA,QACvB,KAAK,QAAQ;AAAA,QACb,QAAQ,UAAU;AAAA,QAClB,YAAY,UAAU;AAAA,QACtB;AAAA,QACA,MAAM,QAAQ;AAAA,QACd,SAAS,QAAQ;AAAA,QACjB,eAAe,QAAQ;AAAA,MACxB;AAGA,YAAM,UAAU,QAAQ,IAAI,kBAAkB;AAU9C,UAAI,CAAC,QAAQ,YAAY,QAAQ,WAAW,UAAU,YAAY,QAAQ,UAAU,eAAe,OAAO,UAAU,eAAe,KAAK;AACvI,mBAAW,IAAI,SAAS,MAAM,eAAe;AAC7C,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAOA,YAAM,cAAc;AAAA,QACnB,OAAO,iBAAAC,QAAK;AAAA,QACZ,aAAa,iBAAAA,QAAK;AAAA,MACnB;AAGA,UAAI,YAAY,UAAU,YAAY,UAAU;AAC/C,mBAAO,oBAAAD,UAAK,MAAM,iBAAAC,QAAK,aAAa,WAAW,GAAG,WAAS;AAC1D,cAAI,OAAO;AACV,mBAAO,KAAK;AAAA,UACb;AAAA,QACD,CAAC;AACD,mBAAW,IAAI,SAAS,MAAM,eAAe;AAC7C,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAGA,UAAI,YAAY,aAAa,YAAY,aAAa;AAGrD,cAAM,UAAM,oBAAAD,UAAK,WAAW,IAAI,gCAAY,GAAG,WAAS;AACvD,cAAI,OAAO;AACV,mBAAO,KAAK;AAAA,UACb;AAAA,QACD,CAAC;AACD,YAAI,KAAK,QAAQ,WAAS;AAEzB,eAAK,MAAM,CAAC,IAAI,QAAU,GAAM;AAC/B,uBAAO,oBAAAA,UAAK,MAAM,iBAAAC,QAAK,cAAc,GAAG,WAAS;AAChD,kBAAI,OAAO;AACV,uBAAO,KAAK;AAAA,cACb;AAAA,YACD,CAAC;AAAA,UACF,OAAO;AACN,uBAAO,oBAAAD,UAAK,MAAM,iBAAAC,QAAK,iBAAiB,GAAG,WAAS;AACnD,kBAAI,OAAO;AACV,uBAAO,KAAK;AAAA,cACb;AAAA,YACD,CAAC;AAAA,UACF;AAEA,qBAAW,IAAI,SAAS,MAAM,eAAe;AAC7C,kBAAQ,QAAQ;AAAA,QACjB,CAAC;AACD,YAAI,KAAK,OAAO,MAAM;AAGrB,cAAI,CAAC,UAAU;AACd,uBAAW,IAAI,SAAS,MAAM,eAAe;AAC7C,oBAAQ,QAAQ;AAAA,UACjB;AAAA,QACD,CAAC;AACD;AAAA,MACD;AAGA,UAAI,YAAY,MAAM;AACrB,mBAAO,oBAAAD,UAAK,MAAM,iBAAAC,QAAK,uBAAuB,GAAG,WAAS;AACzD,cAAI,OAAO;AACV,mBAAO,KAAK;AAAA,UACb;AAAA,QACD,CAAC;AACD,mBAAW,IAAI,SAAS,MAAM,eAAe;AAC7C,gBAAQ,QAAQ;AAChB;AAAA,MACD;AAGA,iBAAW,IAAI,SAAS,MAAM,eAAe;AAC7C,cAAQ,QAAQ;AAAA,IACjB,CAAC;AAGD,kBAAc,UAAU,OAAO,EAAE,MAAM,MAAM;AAAA,EAC9C,CAAC;AACF;AAEA,SAAS,oCAAoC,SAAS,eAAe;AACpE,QAAM,aAAa,2BAAO,KAAK,WAAW;AAE1C,MAAI,oBAAoB;AACxB,MAAI,0BAA0B;AAC9B,MAAI;AAEJ,UAAQ,GAAG,YAAY,cAAY;AAClC,UAAM,EAAC,QAAO,IAAI;AAClB,wBAAoB,QAAQ,mBAAmB,MAAM,aAAa,CAAC,QAAQ,gBAAgB;AAAA,EAC5F,CAAC;AAED,UAAQ,GAAG,UAAU,YAAU;AAC9B,UAAM,gBAAgB,MAAM;AAC3B,UAAI,qBAAqB,CAAC,yBAAyB;AAClD,cAAM,QAAQ,IAAI,MAAM,iBAAiB;AACzC,cAAM,OAAO;AACb,sBAAc,KAAK;AAAA,MACpB;AAAA,IACD;AAEA,UAAM,SAAS,SAAO;AACrB,gCAA0B,2BAAO,QAAQ,IAAI,MAAM,EAAE,GAAG,UAAU,MAAM;AAGxE,UAAI,CAAC,2BAA2B,eAAe;AAC9C,kCACC,2BAAO,QAAQ,cAAc,MAAM,EAAE,GAAG,WAAW,MAAM,GAAG,CAAC,CAAC,MAAM,KACpE,2BAAO,QAAQ,IAAI,MAAM,EAAE,GAAG,WAAW,MAAM,CAAC,CAAC,MAAM;AAAA,MAEzD;AAEA,sBAAgB;AAAA,IACjB;AAEA,WAAO,gBAAgB,SAAS,aAAa;AAC7C,WAAO,GAAG,QAAQ,MAAM;AAExB,YAAQ,GAAG,SAAS,MAAM;AACzB,aAAO,eAAe,SAAS,aAAa;AAC5C,aAAO,eAAe,QAAQ,MAAM;AAAA,IACrC,CAAC;AAAA,EACF,CAAC;AACF;", "names": ["import_node_http", "import_node_stream", "import_node_buffer", "Stream", "import_node_util", "http", "INTERNALS", "import_node_util", "INTERNALS", "formatUrl", "response", "https", "http", "Stream", "pump", "zlib"]}