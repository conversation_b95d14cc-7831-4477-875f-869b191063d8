import {
  createReactComponent
} from "./chunk-6OXWZ7JH.js";

// node_modules/@tabler/icons-react/dist/esm/icons/IconShieldHalf.mjs
var IconShieldHalf = createReactComponent("outline", "shield-half", "IconShieldHalf", [["path", { "d": "M12 3a12 12 0 0 0 8.5 3a12 12 0 0 1 -8.5 15a12 12 0 0 1 -8.5 -15a12 12 0 0 0 8.5 -3", "key": "svg-0" }], ["path", { "d": "M12 3v18", "key": "svg-1" }]]);

export {
  IconShieldHalf
};
/*! Bundled license information:

@tabler/icons-react/dist/esm/icons/IconShieldHalf.mjs:
  (**
   * @license @tabler/icons-react v3.34.0 - MIT
   *
   * This source code is licensed under the MIT license.
   * See the LICENSE file in the root directory of this source tree.
   *)
*/
//# sourceMappingURL=chunk-ZZKUIT42.js.map
