import { sqliteTable, text, integer, real, blob } from 'drizzle-orm/sqlite-core';
import { sql } from 'drizzle-orm';

// Companies table
export const companies = sqliteTable('companies', {
  id: text('id').primaryKey(),
  companyCode: text('company_code').notNull().unique(), // For login identification
  name: text('name').notNull(),
  nameAr: text('name_ar').notNull(),
  logo: text('logo'),
  address: text('address'),
  phone: text('phone'),
  email: text('email'),
  taxNumber: text('tax_number'),
  crNumber: text('cr_number'),
  isSetupComplete: integer('is_setup_complete', { mode: 'boolean' }).default(false),
  subscriptionPlan: text('subscription_plan').default('basic'),
  subscriptionExpiry: integer('subscription_expiry', { mode: 'timestamp' }),
  settings: text('settings', { mode: 'json' }),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Branches table
export const branches = sqliteTable('branches', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  name: text('name').notNull(),
  nameAr: text('name_ar').notNull(),
  address: text('address'),
  phone: text('phone'),
  email: text('email'),
  isMain: integer('is_main', { mode: 'boolean' }).default(false),
  zeroTierNetworkId: text('zerotier_network_id'),
  settings: text('settings', { mode: 'json' }),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Company owners table
export const companyOwners = sqliteTable('company_owners', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  email: text('email').notNull().unique(),
  passwordHash: text('password_hash').notNull(),
  fullName: text('full_name').notNull(),
  fullNameAr: text('full_name_ar'),
  phone: text('phone'),
  avatar: text('avatar'),
  isEmailVerified: integer('is_email_verified', { mode: 'boolean' }).default(false),
  emailVerificationToken: text('email_verification_token'),
  passwordResetToken: text('password_reset_token'),
  passwordResetExpiry: integer('password_reset_expiry', { mode: 'timestamp' }),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  lastLogin: integer('last_login', { mode: 'timestamp' }),
  sessionTimeout: integer('session_timeout').default(7200), // 2 hours in seconds
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Employees table
export const employees = sqliteTable('employees', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  branchId: text('branch_id').notNull().references(() => branches.id),
  employeeNumber: text('employee_number').notNull(),
  passwordHash: text('password_hash').notNull(),
  fullName: text('full_name').notNull(),
  fullNameAr: text('full_name_ar'),
  email: text('email'),
  phone: text('phone'),
  role: text('role').notNull(), // MANAGER, CASHIER, SALESPERSON, ACCOUNTANT, etc.
  permissions: text('permissions', { mode: 'json' }),
  avatar: text('avatar'),
  salary: real('salary'),
  hireDate: integer('hire_date', { mode: 'timestamp' }),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  requiresApproval: integer('requires_approval', { mode: 'boolean' }).default(true),
  approvedBy: text('approved_by').references(() => companyOwners.id),
  approvedAt: integer('approved_at', { mode: 'timestamp' }),
  lastLogin: integer('last_login', { mode: 'timestamp' }),
  sessionTimeout: integer('session_timeout').default(28800), // 8 hours in seconds
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Product categories
export const productCategories = sqliteTable('product_categories', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  name: text('name').notNull(),
  nameAr: text('name_ar').notNull(),
  description: text('description'),
  parentId: text('parent_id').references(() => productCategories.id),
  image: text('image'),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Product categories table
export const productCategories = sqliteTable('product_categories', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  parentId: text('parent_id').references(() => productCategories.id),
  name: text('name').notNull(),
  nameAr: text('name_ar').notNull(),
  description: text('description'),
  descriptionAr: text('description_ar'),
  icon: text('icon'),
  color: text('color'),
  sortOrder: integer('sort_order').default(0),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Units of measurement table
export const units = sqliteTable('units', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  name: text('name').notNull(),
  nameAr: text('name_ar').notNull(),
  symbol: text('symbol').notNull(),
  symbolAr: text('symbol_ar'),
  type: text('type').notNull(), // 'base', 'derived'
  baseUnitId: text('base_unit_id').references(() => units.id),
  conversionFactor: real('conversion_factor').default(1),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Products table (enhanced)
export const products = sqliteTable('products', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  categoryId: text('category_id').references(() => productCategories.id),
  name: text('name').notNull(),
  nameAr: text('name_ar').notNull(),
  description: text('description'),
  descriptionAr: text('description_ar'),
  sku: text('sku').notNull().unique(),
  barcode: text('barcode'),
  brand: text('brand'),
  brandAr: text('brand_ar'),
  model: text('model'),
  year: text('year'),
  weight: real('weight'),
  dimensions: text('dimensions', { mode: 'json' }), // {length, width, height}
  images: text('images', { mode: 'json' }), // Array of image URLs
  baseUnitId: text('base_unit_id').notNull().references(() => units.id),
  unitConversions: text('unit_conversions', { mode: 'json' }), // Array of unit conversions
  costPrice: real('cost_price').notNull(),
  sellingPrice: real('selling_price').notNull(),
  wholesalePrice: real('wholesale_price'),
  minStock: integer('min_stock').default(0),
  maxStock: integer('max_stock'),
  reorderLevel: integer('reorder_level'),
  reorderQuantity: integer('reorder_quantity'),
  location: text('location'), // Storage location
  tags: text('tags', { mode: 'json' }), // Array of tags
  attributes: text('attributes', { mode: 'json' }), // Custom attributes
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  isFeatured: integer('is_featured', { mode: 'boolean' }).default(false),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Product OEM numbers table
export const productOemNumbers = sqliteTable('product_oem_numbers', {
  id: text('id').primaryKey(),
  productId: text('product_id').notNull().references(() => products.id, { onDelete: 'cascade' }),
  oemNumber: text('oem_number').notNull(),
  manufacturer: text('manufacturer'),
  manufacturerAr: text('manufacturer_ar'),
  isOriginal: integer('is_original', { mode: 'boolean' }).default(false),
  notes: text('notes'),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Product suppliers table
export const productSuppliers = sqliteTable('product_suppliers', {
  id: text('id').primaryKey(),
  productId: text('product_id').notNull().references(() => products.id, { onDelete: 'cascade' }),
  supplierId: text('supplier_id').notNull().references(() => suppliers.id),
  supplierSku: text('supplier_sku'),
  supplierPrice: real('supplier_price'),
  minimumOrderQuantity: integer('minimum_order_quantity').default(1),
  leadTimeDays: integer('lead_time_days'),
  isPreferred: integer('is_preferred', { mode: 'boolean' }).default(false),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Product price history table
export const productPriceHistory = sqliteTable('product_price_history', {
  id: text('id').primaryKey(),
  productId: text('product_id').notNull().references(() => products.id, { onDelete: 'cascade' }),
  priceType: text('price_type').notNull(), // 'cost', 'selling', 'wholesale'
  oldPrice: real('old_price').notNull(),
  newPrice: real('new_price').notNull(),
  changeReason: text('change_reason'),
  changedBy: text('changed_by').notNull(),
  changedAt: integer('changed_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Product templates table
export const productTemplates = sqliteTable('product_templates', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  name: text('name').notNull(),
  nameAr: text('name_ar').notNull(),
  type: text('type').notNull(), // 'oil', 'tire', 'battery', 'general'
  fields: text('fields', { mode: 'json' }), // Template field definitions
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});



// Inventory table
export const inventory = sqliteTable('inventory', {
  id: text('id').primaryKey(),
  branchId: text('branch_id').notNull().references(() => branches.id),
  productId: text('product_id').notNull().references(() => products.id),
  quantity: real('quantity').notNull().default(0),
  reservedQuantity: real('reserved_quantity').notNull().default(0),
  location: text('location'),
  lastMovement: integer('last_movement', { mode: 'timestamp' }),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Customers table
export const customers = sqliteTable('customers', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  code: text('code').notNull(),
  name: text('name').notNull(),
  nameAr: text('name_ar'),
  phone: text('phone'),
  email: text('email'),
  address: text('address'),
  taxNumber: text('tax_number'),
  creditLimit: real('credit_limit').default(0),
  currentBalance: real('current_balance').default(0),
  loyaltyPoints: integer('loyalty_points').default(0),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Suppliers table
export const suppliers = sqliteTable('suppliers', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  code: text('code').notNull(),
  name: text('name').notNull(),
  nameAr: text('name_ar'),
  phone: text('phone'),
  email: text('email'),
  address: text('address'),
  taxNumber: text('tax_number'),
  currentBalance: real('current_balance').default(0),
  paymentTerms: text('payment_terms'),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Sales invoices
export const salesInvoices = sqliteTable('sales_invoices', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  branchId: text('branch_id').notNull().references(() => branches.id),
  customerId: text('customer_id').references(() => customers.id),
  invoiceNumber: text('invoice_number').notNull(),
  invoiceDate: integer('invoice_date', { mode: 'timestamp' }).notNull(),
  dueDate: integer('due_date', { mode: 'timestamp' }),
  subtotal: real('subtotal').notNull(),
  taxAmount: real('tax_amount').notNull(),
  discountAmount: real('discount_amount').default(0),
  totalAmount: real('total_amount').notNull(),
  paidAmount: real('paid_amount').default(0),
  status: text('status').notNull(), // draft, confirmed, paid, cancelled
  paymentMethod: text('payment_method'),
  notes: text('notes'),
  zatcaUuid: text('zatca_uuid'),
  zatcaQr: text('zatca_qr'),
  createdByType: text('created_by_type').notNull(), // 'OWNER' or 'EMPLOYEE'
  createdById: text('created_by_id').notNull(), // References either companyOwners.id or employees.id
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Sales invoice items
export const salesInvoiceItems = sqliteTable('sales_invoice_items', {
  id: text('id').primaryKey(),
  invoiceId: text('invoice_id').notNull().references(() => salesInvoices.id),
  productId: text('product_id').notNull().references(() => products.id),
  quantity: real('quantity').notNull(),
  unitPrice: real('unit_price').notNull(),
  discountPercent: real('discount_percent').default(0),
  taxPercent: real('tax_percent').default(15),
  totalAmount: real('total_amount').notNull(),
});

// Authentication sessions
export const authSessions = sqliteTable('auth_sessions', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  userType: text('user_type').notNull(), // 'OWNER' or 'EMPLOYEE'
  userId: text('user_id').notNull(), // References either companyOwners.id or employees.id
  token: text('token').notNull().unique(),
  refreshToken: text('refresh_token').notNull().unique(),
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  expiresAt: integer('expires_at', { mode: 'timestamp' }).notNull(),
  isActive: integer('is_active', { mode: 'boolean' }).default(true),
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Password policies
export const passwordPolicies = sqliteTable('password_policies', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  userType: text('user_type').notNull(), // 'OWNER' or 'EMPLOYEE'
  minLength: integer('min_length').notNull(),
  requireUppercase: integer('require_uppercase', { mode: 'boolean' }).default(true),
  requireLowercase: integer('require_lowercase', { mode: 'boolean' }).default(true),
  requireNumbers: integer('require_numbers', { mode: 'boolean' }).default(true),
  requireSpecialChars: integer('require_special_chars', { mode: 'boolean' }).default(true),
  maxAge: integer('max_age'), // Days before password expires
  preventReuse: integer('prevent_reuse').default(5), // Number of previous passwords to prevent reuse
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
  updatedAt: integer('updated_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});

// Audit log for all operations
export const auditLog = sqliteTable('audit_log', {
  id: text('id').primaryKey(),
  companyId: text('company_id').notNull().references(() => companies.id),
  branchId: text('branch_id').references(() => branches.id),
  userType: text('user_type'), // 'OWNER' or 'EMPLOYEE'
  userId: text('user_id'), // References either companyOwners.id or employees.id
  action: text('action').notNull(),
  tableName: text('table_name').notNull(),
  recordId: text('record_id'),
  oldValues: text('old_values', { mode: 'json' }),
  newValues: text('new_values', { mode: 'json' }),
  ipAddress: text('ip_address'),
  userAgent: text('user_agent'),
  severity: text('severity').default('INFO'), // INFO, WARNING, ERROR, CRITICAL
  createdAt: integer('created_at', { mode: 'timestamp' }).default(sql`CURRENT_TIMESTAMP`),
});
