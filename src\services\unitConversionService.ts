// Unit conversion service for hierarchical units with automatic price calculation

export interface Unit {
  id: string;
  name: string;
  nameAr: string;
  symbol: string;
  symbolAr?: string;
  type: 'base' | 'derived';
  baseUnitId?: string;
  conversionFactor: number;
  isActive: boolean;
}

export interface UnitConversion {
  unitId: string;
  conversionFactor: number;
  price?: number;
  isDefault?: boolean;
}

export interface ProductUnit {
  productId: string;
  baseUnitId: string;
  conversions: UnitConversion[];
}

export class UnitConversionService {
  private static instance: UnitConversionService;
  private units: Map<string, Unit> = new Map();
  private conversionCache: Map<string, number> = new Map();

  private constructor() {
    this.initializeDefaultUnits();
  }

  static getInstance(): UnitConversionService {
    if (!UnitConversionService.instance) {
      UnitConversionService.instance = new UnitConversionService();
    }
    return UnitConversionService.instance;
  }

  // Initialize default units for auto parts
  private initializeDefaultUnits(): void {
    const defaultUnits: Unit[] = [
      // Base units
      {
        id: 'piece',
        name: 'Piece',
        nameAr: 'قطعة',
        symbol: 'pc',
        symbolAr: 'ق',
        type: 'base',
        conversionFactor: 1,
        isActive: true,
      },
      {
        id: 'liter',
        name: 'Liter',
        nameAr: 'لتر',
        symbol: 'L',
        symbolAr: 'ل',
        type: 'base',
        conversionFactor: 1,
        isActive: true,
      },
      {
        id: 'kilogram',
        name: 'Kilogram',
        nameAr: 'كيلوجرام',
        symbol: 'kg',
        symbolAr: 'كج',
        type: 'base',
        conversionFactor: 1,
        isActive: true,
      },
      {
        id: 'meter',
        name: 'Meter',
        nameAr: 'متر',
        symbol: 'm',
        symbolAr: 'م',
        type: 'base',
        conversionFactor: 1,
        isActive: true,
      },
      
      // Derived units for pieces
      {
        id: 'pair',
        name: 'Pair',
        nameAr: 'زوج',
        symbol: 'pr',
        symbolAr: 'ز',
        type: 'derived',
        baseUnitId: 'piece',
        conversionFactor: 2,
        isActive: true,
      },
      {
        id: 'dozen',
        name: 'Dozen',
        nameAr: 'دزينة',
        symbol: 'dz',
        symbolAr: 'دز',
        type: 'derived',
        baseUnitId: 'piece',
        conversionFactor: 12,
        isActive: true,
      },
      {
        id: 'box',
        name: 'Box',
        nameAr: 'صندوق',
        symbol: 'box',
        symbolAr: 'صند',
        type: 'derived',
        baseUnitId: 'piece',
        conversionFactor: 24, // Default, can be customized per product
        isActive: true,
      },
      {
        id: 'case',
        name: 'Case',
        nameAr: 'كرتونة',
        symbol: 'case',
        symbolAr: 'كرت',
        type: 'derived',
        baseUnitId: 'piece',
        conversionFactor: 48, // Default, can be customized per product
        isActive: true,
      },
      
      // Volume units
      {
        id: 'milliliter',
        name: 'Milliliter',
        nameAr: 'مليلتر',
        symbol: 'ml',
        symbolAr: 'مل',
        type: 'derived',
        baseUnitId: 'liter',
        conversionFactor: 0.001,
        isActive: true,
      },
      {
        id: 'gallon',
        name: 'Gallon',
        nameAr: 'جالون',
        symbol: 'gal',
        symbolAr: 'جال',
        type: 'derived',
        baseUnitId: 'liter',
        conversionFactor: 3.78541,
        isActive: true,
      },
      
      // Weight units
      {
        id: 'gram',
        name: 'Gram',
        nameAr: 'جرام',
        symbol: 'g',
        symbolAr: 'ج',
        type: 'derived',
        baseUnitId: 'kilogram',
        conversionFactor: 0.001,
        isActive: true,
      },
      {
        id: 'ton',
        name: 'Ton',
        nameAr: 'طن',
        symbol: 't',
        symbolAr: 'ط',
        type: 'derived',
        baseUnitId: 'kilogram',
        conversionFactor: 1000,
        isActive: true,
      },
      
      // Length units
      {
        id: 'centimeter',
        name: 'Centimeter',
        nameAr: 'سنتيمتر',
        symbol: 'cm',
        symbolAr: 'سم',
        type: 'derived',
        baseUnitId: 'meter',
        conversionFactor: 0.01,
        isActive: true,
      },
      {
        id: 'inch',
        name: 'Inch',
        nameAr: 'بوصة',
        symbol: 'in',
        symbolAr: 'بوصة',
        type: 'derived',
        baseUnitId: 'meter',
        conversionFactor: 0.0254,
        isActive: true,
      },
    ];

    defaultUnits.forEach(unit => {
      this.units.set(unit.id, unit);
    });
  }

  // Convert quantity from one unit to another
  convertQuantity(quantity: number, fromUnitId: string, toUnitId: string): number {
    if (fromUnitId === toUnitId) {
      return quantity;
    }

    const cacheKey = `${fromUnitId}-${toUnitId}`;
    let conversionFactor = this.conversionCache.get(cacheKey);

    if (conversionFactor === undefined) {
      conversionFactor = this.calculateConversionFactor(fromUnitId, toUnitId);
      this.conversionCache.set(cacheKey, conversionFactor);
    }

    return quantity * conversionFactor;
  }

  // Calculate conversion factor between two units
  private calculateConversionFactor(fromUnitId: string, toUnitId: string): number {
    const fromUnit = this.units.get(fromUnitId);
    const toUnit = this.units.get(toUnitId);

    if (!fromUnit || !toUnit) {
      throw new Error(`Unit not found: ${fromUnitId} or ${toUnitId}`);
    }

    // Get base unit values for both units
    const fromBaseValue = this.getBaseUnitValue(fromUnit);
    const toBaseValue = this.getBaseUnitValue(toUnit);

    // Check if units are compatible (same base unit)
    const fromBaseUnit = this.getBaseUnit(fromUnit);
    const toBaseUnit = this.getBaseUnit(toUnit);

    if (fromBaseUnit.id !== toBaseUnit.id) {
      throw new Error(`Incompatible units: ${fromUnit.nameAr} and ${toUnit.nameAr}`);
    }

    return fromBaseValue / toBaseValue;
  }

  // Get the base unit value (conversion factor to base unit)
  private getBaseUnitValue(unit: Unit): number {
    if (unit.type === 'base') {
      return 1;
    }

    if (!unit.baseUnitId) {
      throw new Error(`Derived unit ${unit.id} missing base unit ID`);
    }

    const baseUnit = this.units.get(unit.baseUnitId);
    if (!baseUnit) {
      throw new Error(`Base unit not found: ${unit.baseUnitId}`);
    }

    return unit.conversionFactor * this.getBaseUnitValue(baseUnit);
  }

  // Get the ultimate base unit for a unit
  private getBaseUnit(unit: Unit): Unit {
    if (unit.type === 'base') {
      return unit;
    }

    if (!unit.baseUnitId) {
      throw new Error(`Derived unit ${unit.id} missing base unit ID`);
    }

    const baseUnit = this.units.get(unit.baseUnitId);
    if (!baseUnit) {
      throw new Error(`Base unit not found: ${unit.baseUnitId}`);
    }

    return this.getBaseUnit(baseUnit);
  }

  // Calculate price for a specific unit based on base price
  calculateUnitPrice(basePrice: number, baseUnitId: string, targetUnitId: string): number {
    if (baseUnitId === targetUnitId) {
      return basePrice;
    }

    const conversionFactor = this.calculateConversionFactor(baseUnitId, targetUnitId);
    return basePrice * conversionFactor;
  }

  // Get all units compatible with a base unit
  getCompatibleUnits(baseUnitId: string): Unit[] {
    const baseUnit = this.units.get(baseUnitId);
    if (!baseUnit) {
      return [];
    }

    const ultimateBaseUnit = this.getBaseUnit(baseUnit);
    
    return Array.from(this.units.values()).filter(unit => {
      try {
        const unitBaseUnit = this.getBaseUnit(unit);
        return unitBaseUnit.id === ultimateBaseUnit.id && unit.isActive;
      } catch {
        return false;
      }
    });
  }

  // Get unit by ID
  getUnit(unitId: string): Unit | undefined {
    return this.units.get(unitId);
  }

  // Get all units
  getAllUnits(): Unit[] {
    return Array.from(this.units.values()).filter(unit => unit.isActive);
  }

  // Add custom unit
  addUnit(unit: Unit): void {
    this.units.set(unit.id, unit);
    this.clearCache();
  }

  // Update unit
  updateUnit(unitId: string, updates: Partial<Unit>): void {
    const unit = this.units.get(unitId);
    if (unit) {
      this.units.set(unitId, { ...unit, ...updates });
      this.clearCache();
    }
  }

  // Clear conversion cache
  private clearCache(): void {
    this.conversionCache.clear();
  }

  // Format unit display
  formatUnit(unitId: string, locale: 'ar' | 'en' = 'ar'): string {
    const unit = this.units.get(unitId);
    if (!unit) {
      return unitId;
    }

    if (locale === 'ar') {
      return unit.symbolAr || unit.nameAr;
    } else {
      return unit.symbol || unit.name;
    }
  }

  // Create product unit conversions
  createProductUnitConversions(
    productId: string,
    baseUnitId: string,
    basePrice: number,
    customConversions: { unitId: string; factor?: number; price?: number }[] = []
  ): ProductUnit {
    const compatibleUnits = this.getCompatibleUnits(baseUnitId);
    const conversions: UnitConversion[] = [];

    // Add base unit
    conversions.push({
      unitId: baseUnitId,
      conversionFactor: 1,
      price: basePrice,
      isDefault: true,
    });

    // Add compatible units
    compatibleUnits.forEach(unit => {
      if (unit.id !== baseUnitId) {
        const customConversion = customConversions.find(c => c.unitId === unit.id);
        const conversionFactor = customConversion?.factor || 
          this.calculateConversionFactor(baseUnitId, unit.id);
        
        const price = customConversion?.price || 
          this.calculateUnitPrice(basePrice, baseUnitId, unit.id);

        conversions.push({
          unitId: unit.id,
          conversionFactor,
          price,
          isDefault: false,
        });
      }
    });

    return {
      productId,
      baseUnitId,
      conversions,
    };
  }
}

// Export singleton instance
export const unitConversionService = UnitConversionService.getInstance();
