{"name": "dimensions-erp", "version": "1.0.0", "description": "Comprehensive Electron-based ERP system for auto parts retail", "main": "dist/main.js", "homepage": "./", "scripts": {"dev": "concurrently \"npm run dev:vite\" \"wait-on http://localhost:5173 && npm run dev:electron\"", "dev:vite": "vite", "dev:electron": "cross-env NODE_ENV=development electron .", "build": "tsc && vite build", "build:electron": "electron-builder", "dist": "npm run build && npm run build:electron", "preview": "vite preview", "postinstall": "electron-builder install-app-deps"}, "keywords": ["erp", "electron", "auto-parts", "retail", "arabic", "rtl"], "author": "Dimensions ERP Team", "license": "MIT", "type": "commonjs", "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@mantine/charts": "^7.17.8", "@mantine/core": "^7.17.8", "@mantine/dates": "^7.17.8", "@mantine/form": "^7.17.8", "@mantine/hooks": "^7.17.8", "@mantine/modals": "^7.17.8", "@mantine/notifications": "^7.17.8", "@mantine/spotlight": "^7.17.8", "@tabler/icons-react": "^3.34.0", "@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.0.10", "@types/node-fetch": "^2.6.12", "@types/nodemailer": "^6.4.17", "@types/pg": "^8.15.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-grid-layout": "^1.3.5", "@types/react-image-gallery": "^1.2.4", "@types/react-webcam": "^1.1.0", "@types/react-window": "^1.8.8", "@types/validator": "^13.15.2", "@types/ws": "^8.18.1", "bcryptjs": "^3.0.2", "better-sqlite3": "^12.2.0", "bonjour-service": "^1.3.0", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.2", "electron": "^28.3.3", "electron-store": "^10.1.0", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "jsonwebtoken": "^9.0.2", "node-fetch": "^3.3.2", "nodemailer": "^7.0.4", "pg": "^8.16.3", "ping": "^0.4.4", "quagga": "^0.12.1", "react": "^18.3.1", "react-barcode-reader": "^0.0.2", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.2", "react-i18next": "^15.6.0", "react-image-gallery": "^1.4.0", "react-webcam": "^7.2.0", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10", "recharts": "^3.0.2", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "typescript": "^5.8.3", "validator": "^13.15.15", "ws": "^8.18.3", "y-websocket": "^3.0.0", "yjs": "^13.6.27", "zustand": "^5.0.6"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.6.0", "concurrently": "^9.2.0", "cross-env": "^7.0.3", "electron-builder": "^26.0.12", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.5", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "prettier": "^3.6.2", "vite": "^7.0.2", "wait-on": "^8.0.3"}, "build": {"appId": "com.dimensions.erp", "productName": "Dimensions ERP", "directories": {"output": "release"}, "files": ["dist/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "data", "to": "data", "filter": ["**/*"]}], "win": {"target": "nsis", "icon": "assets/icon.ico"}, "mac": {"target": "dmg", "icon": "assets/icon.icns"}, "linux": {"target": "AppImage", "icon": "assets/icon.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true}}}