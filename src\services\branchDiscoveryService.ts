import Bonjour, { Service, Browser } from 'bonjour-service';
import { EventEmitter } from 'events';
import { zeroTierService } from './zeroTierService';

export interface DiscoveredBranch {
  id: string;
  name: string;
  nameAr: string;
  companyId: string;
  zeroTierAddress: string;
  ipAddress: string;
  port: number;
  lastSeen: Date;
  isOnline: boolean;
  latency?: number;
  version: string;
  capabilities: string[];
}

export interface BranchServiceInfo {
  branchId: string;
  branchName: string;
  branchNameAr: string;
  companyId: string;
  version: string;
  capabilities: string[];
  zeroTierAddress: string;
}

export class BranchDiscoveryService extends EventEmitter {
  private static instance: BranchDiscoveryService;
  private bonjour: Bonjour;
  private discoveredBranches: Map<string, DiscoveredBranch> = new Map();
  private myService: Service | null = null;
  private browser: Browser | null = null;
  private isRunning: boolean = false;
  private healthCheckInterval: NodeJS.Timeout | null = null;

  private constructor() {
    super();
    this.bonjour = new Bonjour();
  }

  static getInstance(): BranchDiscoveryService {
    if (!BranchDiscoveryService.instance) {
      BranchDiscoveryService.instance = new BranchDiscoveryService();
    }
    return BranchDiscoveryService.instance;
  }

  // Start discovery service
  async start(branchInfo: BranchServiceInfo, port: number = 8080): Promise<void> {
    if (this.isRunning) {
      console.log('Branch discovery service is already running');
      return;
    }

    try {
      // Get ZeroTier address
      const zeroTierAddress = await zeroTierService.getMyAddress();
      
      // Publish our service
      await this.publishService({
        ...branchInfo,
        zeroTierAddress,
      }, port);

      // Start browsing for other branches
      this.startBrowsing();

      // Start health check
      this.startHealthCheck();

      this.isRunning = true;
      console.log('Branch discovery service started');
      this.emit('started');
    } catch (error) {
      console.error('Failed to start branch discovery service:', error);
      throw error;
    }
  }

  // Stop discovery service
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    try {
      // Unpublish our service
      if (this.myService) {
        this.myService.stop();
        this.myService = null;
      }

      // Stop browsing
      if (this.browser) {
        this.browser.stop();
        this.browser = null;
      }

      // Stop health check
      if (this.healthCheckInterval) {
        clearInterval(this.healthCheckInterval);
        this.healthCheckInterval = null;
      }

      this.isRunning = false;
      console.log('Branch discovery service stopped');
      this.emit('stopped');
    } catch (error) {
      console.error('Failed to stop branch discovery service:', error);
      throw error;
    }
  }

  // Publish our branch service
  private async publishService(branchInfo: BranchServiceInfo, port: number): Promise<void> {
    try {
      this.myService = this.bonjour.publish({
        name: `${branchInfo.branchName}-${branchInfo.branchId}`,
        type: 'dimensions-erp',
        port: port,
        txt: {
          branchId: branchInfo.branchId,
          branchName: branchInfo.branchName,
          branchNameAr: branchInfo.branchNameAr,
          companyId: branchInfo.companyId,
          version: branchInfo.version,
          capabilities: branchInfo.capabilities.join(','),
          zeroTierAddress: branchInfo.zeroTierAddress,
        },
      });

      console.log(`Published branch service: ${branchInfo.branchName} on port ${port}`);
    } catch (error) {
      console.error('Failed to publish service:', error);
      throw error;
    }
  }

  // Start browsing for other branches
  private startBrowsing(): void {
    this.browser = this.bonjour.find({ type: 'dimensions-erp' }, (service) => {
      this.handleServiceUp(service);
    });

    this.browser.on('down', (service) => {
      this.handleServiceDown(service);
    });

    console.log('Started browsing for branch services');
  }

  // Handle discovered service
  private handleServiceUp(service: Service): void {
    try {
      const txt = service.txt || {};
      const branchId = txt.branchId;

      if (!branchId) {
        console.warn('Discovered service without branchId:', service);
        return;
      }

      // Don't add ourselves
      if (this.myService && branchId === this.myService.txt?.branchId) {
        return;
      }

      const branch: DiscoveredBranch = {
        id: branchId,
        name: txt.branchName || service.name,
        nameAr: txt.branchNameAr || txt.branchName || service.name,
        companyId: txt.companyId || '',
        zeroTierAddress: txt.zeroTierAddress || '',
        ipAddress: service.addresses?.[0] || '',
        port: service.port,
        lastSeen: new Date(),
        isOnline: true,
        version: txt.version || '1.0.0',
        capabilities: txt.capabilities ? txt.capabilities.split(',') : [],
      };

      this.discoveredBranches.set(branchId, branch);
      console.log(`Discovered branch: ${branch.name} (${branch.id})`);
      this.emit('branchDiscovered', branch);
    } catch (error) {
      console.error('Error handling service up:', error);
    }
  }

  // Handle service going down
  private handleServiceDown(service: Service): void {
    try {
      const txt = service.txt || {};
      const branchId = txt.branchId;

      if (!branchId) {
        return;
      }

      const branch = this.discoveredBranches.get(branchId);
      if (branch) {
        branch.isOnline = false;
        branch.lastSeen = new Date();
        console.log(`Branch went offline: ${branch.name} (${branch.id})`);
        this.emit('branchOffline', branch);
      }
    } catch (error) {
      console.error('Error handling service down:', error);
    }
  }

  // Start health check for discovered branches
  private startHealthCheck(): void {
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 30000); // Every 30 seconds
  }

  // Perform health check on all discovered branches
  private async performHealthCheck(): Promise<void> {
    const promises = Array.from(this.discoveredBranches.values()).map(async (branch) => {
      try {
        const latency = await this.pingBranch(branch);
        branch.latency = latency;
        branch.isOnline = latency !== -1;
        branch.lastSeen = new Date();
        
        if (branch.isOnline) {
          this.emit('branchHealthy', branch);
        } else {
          this.emit('branchUnhealthy', branch);
        }
      } catch (error) {
        branch.isOnline = false;
        this.emit('branchUnhealthy', branch);
      }
    });

    await Promise.allSettled(promises);
  }

  // Ping a branch to check latency
  private async pingBranch(branch: DiscoveredBranch): Promise<number> {
    try {
      const start = Date.now();
      
      // Try to connect to the branch's WebSocket endpoint
      const response = await fetch(`http://${branch.ipAddress}:${branch.port}/health`, {
        method: 'GET',
        timeout: 5000,
      });

      if (response.ok) {
        return Date.now() - start;
      } else {
        return -1;
      }
    } catch (error) {
      return -1;
    }
  }

  // Get all discovered branches
  getDiscoveredBranches(): DiscoveredBranch[] {
    return Array.from(this.discoveredBranches.values());
  }

  // Get online branches only
  getOnlineBranches(): DiscoveredBranch[] {
    return this.getDiscoveredBranches().filter(branch => branch.isOnline);
  }

  // Get branch by ID
  getBranch(branchId: string): DiscoveredBranch | undefined {
    return this.discoveredBranches.get(branchId);
  }

  // Remove stale branches (offline for more than 5 minutes)
  cleanupStaleBranches(): void {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    
    for (const [branchId, branch] of this.discoveredBranches.entries()) {
      if (!branch.isOnline && branch.lastSeen < fiveMinutesAgo) {
        this.discoveredBranches.delete(branchId);
        console.log(`Removed stale branch: ${branch.name} (${branch.id})`);
        this.emit('branchRemoved', branch);
      }
    }
  }

  // Get network statistics
  getNetworkStats(): {
    totalBranches: number;
    onlineBranches: number;
    averageLatency: number;
    healthyBranches: number;
  } {
    const branches = this.getDiscoveredBranches();
    const onlineBranches = branches.filter(b => b.isOnline);
    const healthyBranches = onlineBranches.filter(b => b.latency !== undefined && b.latency < 1000);
    
    const totalLatency = onlineBranches.reduce((sum, b) => sum + (b.latency || 0), 0);
    const averageLatency = onlineBranches.length > 0 ? totalLatency / onlineBranches.length : 0;

    return {
      totalBranches: branches.length,
      onlineBranches: onlineBranches.length,
      averageLatency: Math.round(averageLatency),
      healthyBranches: healthyBranches.length,
    };
  }

  // Check if service is running
  isServiceRunning(): boolean {
    return this.isRunning;
  }
}

// Export singleton instance
export const branchDiscoveryService = BranchDiscoveryService.getInstance();
